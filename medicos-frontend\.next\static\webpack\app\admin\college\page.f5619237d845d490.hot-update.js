"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/college/page",{

/***/ "(app-pages-browser)/./src/lib/api/college.ts":
/*!********************************!*\
  !*** ./src/lib/api/college.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCollege: () => (/* binding */ createCollege),\n/* harmony export */   deleteCollege: () => (/* binding */ deleteCollege),\n/* harmony export */   getCollegeAnalytics: () => (/* binding */ getCollegeAnalytics),\n/* harmony export */   getCollegeById: () => (/* binding */ getCollegeById),\n/* harmony export */   getColleges: () => (/* binding */ getColleges),\n/* harmony export */   getQuestionPaperStatsByDateRange: () => (/* binding */ getQuestionPaperStatsByDateRange),\n/* harmony export */   updateCollege: () => (/* binding */ updateCollege)\n/* harmony export */ });\n/* harmony import */ var _lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/utils/errorHandler */ \"(app-pages-browser)/./src/lib/utils/errorHandler.ts\");\n\n/**\n * Create a new college\n * @param collegeData The college data\n * @returns The created college\n */ async function createCollege(collegeData) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(\"Authentication required\", \"Authentication required. Please log in again.\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(\"\".concat(baseUrl, \"/colleges\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(token)\n            },\n            body: JSON.stringify(collegeData)\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(errorData.message || \"Error: \".concat(response.status), \"Failed to create college. Please try again.\");\n        }\n        const result = await response.json();\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.createSuccessResponse)(result, true, \"College created successfully!\");\n    } catch (error) {\n        console.error(\"Error creating college:\", error);\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(error instanceof Error ? error.message : \"Failed to create college. Please try again.\", \"Failed to create college. Please try again.\");\n    }\n}\n/**\n * Get all colleges\n * @returns List of colleges\n */ async function getColleges() {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(\"Authentication required\", \"Authentication required. Please log in again.\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(\"\".concat(baseUrl, \"/colleges\"), {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(errorData.message || \"Error: \".concat(response.status), \"Failed to load colleges. Please try again.\");\n        }\n        const result = await response.json();\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.createSuccessResponse)(result);\n    } catch (error) {\n        console.error(\"Error fetching colleges:\", error);\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(error instanceof Error ? error.message : \"Failed to load colleges. Please try again.\", \"Failed to load colleges. Please try again.\");\n    }\n}\n/**\n * Delete a college\n * @param id College ID\n * @returns The deleted college\n */ async function deleteCollege(id) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(\"Authentication required\", \"Authentication required. Please log in again.\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(\"\".concat(baseUrl, \"/colleges/\").concat(id), {\n            method: \"DELETE\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(errorData.message || \"Error: \".concat(response.status), \"Failed to delete college. Please try again.\");\n        }\n        const result = await response.json();\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.createSuccessResponse)(result, true, \"College deleted successfully!\");\n    } catch (error) {\n        console.error(\"Error deleting college:\", error);\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(error instanceof Error ? error.message : \"Failed to delete college. Please try again.\", \"Failed to delete college. Please try again.\");\n    }\n}\n/**\n * Get a college by ID\n * @param id College ID\n * @returns The college\n */ async function getCollegeById(id) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(\"Authentication required\", \"Authentication required. Please log in again.\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(\"\".concat(baseUrl, \"/colleges/\").concat(id), {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(errorData.message || \"Error: \".concat(response.status), \"Failed to load college. Please try again.\");\n        }\n        const result = await response.json();\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.createSuccessResponse)(result);\n    } catch (error) {\n        console.error(\"Error fetching college:\", error);\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(error instanceof Error ? error.message : \"Failed to load college. Please try again.\", \"Failed to load college. Please try again.\");\n    }\n}\n/**\n * Update a college\n * @param id College ID\n * @param collegeData The updated college data\n * @returns The updated college\n */ async function updateCollege(id, collegeData) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(\"\".concat(baseUrl, \"/colleges/\").concat(id), {\n            method: \"PUT\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(token)\n            },\n            body: JSON.stringify(collegeData)\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error updating college:\", error);\n        throw error;\n    }\n}\n/**\n * Get a college by ID\n * @param id College ID\n * @returns The college\n */ async function getCollegeAnalytics(id) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(\"\".concat(baseUrl, \"/analytics/college/\").concat(id, \"/summary\"), {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching college:\", error);\n        throw error;\n    }\n}\n/**\n * Get daily-wise question paper statistics by subject for a college\n * @param id College ID\n * @param startDate ISO start date string\n * @param endDate ISO end date string\n * @returns Statistics data\n */ async function getQuestionPaperStatsByDateRange(id, startDate, endDate) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const url = \"\".concat(baseUrl, \"/analytics/college/\").concat(id, \"/question-papers?startDate=\").concat(encodeURIComponent(startDate), \"&endDate=\").concat(encodeURIComponent(endDate));\n        const response = await fetch(url, {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token),\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching question paper stats:\", error);\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/college.ts\n"));

/***/ })

});