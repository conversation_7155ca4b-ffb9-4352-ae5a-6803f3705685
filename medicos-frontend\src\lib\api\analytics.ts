import { apiCall } from '../api';
import { handleApiError, createSuccessResponse, ApiResponse } from '@/lib/utils/errorHandler';

export interface PlatformSummary {
  totalColleges: number;
  totalTeachers: number;
  totalQuestions: number;
  totalPapers: number;
  totalDownloads: number;
  recentActivity: {
    logins: number;
    paperGenerations: number;
    downloads: number;
  };
}

/**
 * Get platform summary statistics
 * @returns Platform summary data
 */
export const getPlatformSummary = async (): Promise<PlatformSummary> => {
  try {
    return await apiCall('/analytics/platform-summary');
  } catch (error: any) {
    console.error('Error fetching platform summary:', error);
    throw new Error(error.message || 'Failed to fetch platform summary');
  }
};

/**
 * Get top colleges by various metrics
 * @returns Top colleges data
 */
export const getTopColleges = async (): Promise<ApiResponse> => {
  try {
    const result = await apiCall('/analytics/top-colleges');
    return createSuccessResponse(result);
  } catch (error: any) {
    console.error('Error fetching top colleges:', error);
    return handleApiError(
      error.message || 'Failed to fetch top colleges data',
      'Failed to load top colleges data. Please try again.'
    );
  }
};

/**
 * Get question usage statistics
 * @returns Question usage data
 */
export const getQuestionUsage = async (): Promise<ApiResponse> => {
  try {
    const result = await apiCall('/analytics/question-usage');
    return createSuccessResponse(result);
  } catch (error: any) {
    console.error('Error fetching question usage:', error);
    return handleApiError(
      error.message || 'Failed to fetch question usage data',
      'Failed to load question usage data. Please try again.'
    );
  }
};

/**
 * Get question statistics
 * @returns Question statistics data
 */
export const getQuestionStats = async (): Promise<ApiResponse> => {
  try {
    const result = await apiCall('/analytics/question-stats');
    return createSuccessResponse(result);
  } catch (error: any) {
    console.error('Error fetching question stats:', error);
    return handleApiError(
      error.message || 'Failed to fetch question statistics',
      'Failed to load question statistics. Please try again.'
    );
  }
};