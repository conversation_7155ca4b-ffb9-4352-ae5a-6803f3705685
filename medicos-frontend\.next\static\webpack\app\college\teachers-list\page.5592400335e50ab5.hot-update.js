"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/college/teachers-list/page",{

/***/ "(app-pages-browser)/./src/app/college/teachers-list/page.tsx":
/*!************************************************!*\
  !*** ./src/app/college/teachers-list/page.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _components_Breadcrumb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Breadcrumb */ \"(app-pages-browser)/./src/components/Breadcrumb.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_table_teachers_table__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/table/teachers-table */ \"(app-pages-browser)/./src/components/table/teachers-table.tsx\");\n/* harmony import */ var _components_TeacherList_Tabs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/TeacherList/Tabs */ \"(app-pages-browser)/./src/components/TeacherList/Tabs.tsx\");\n/* harmony import */ var _components_TeacherList_AddTeacherForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/TeacherList/AddTeacherForm */ \"(app-pages-browser)/./src/components/TeacherList/AddTeacherForm.tsx\");\n/* harmony import */ var _components_TeacherList_EditTeacherForm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/TeacherList/EditTeacherForm */ \"(app-pages-browser)/./src/components/TeacherList/EditTeacherForm.tsx\");\n/* harmony import */ var _lib_api_teachers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/teachers */ \"(app-pages-browser)/./src/lib/api/teachers.ts\");\n/* harmony import */ var _lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils/errorHandler */ \"(app-pages-browser)/./src/lib/utils/errorHandler.ts\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_pagination__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/pagination */ \"(app-pages-browser)/./src/components/ui/pagination.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst TeachersListPage = ()=>{\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('view');\n    const [editingTeacher, setEditingTeacher] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [teacherToDelete, setTeacherToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isFilterModalOpen, setIsFilterModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        department: '',\n        status: 'all',\n        email: ''\n    });\n    const [departments, setDepartments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredTeachers, setFilteredTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [collegeId, setCollegeId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCollegeIdInput, setShowCollegeIdInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [manualCollegeId, setManualCollegeId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    const [totalTeachers, setTotalTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    // Add this function to handle manual collegeId setting\n    const handleManualCollegeIdSubmit = ()=>{\n        if (manualCollegeId.trim()) {\n            console.log(\"Setting manual collegeId:\", manualCollegeId);\n            setCollegeId(manualCollegeId);\n            localStorage.setItem('collegeId', manualCollegeId);\n            setShowCollegeIdInput(false);\n            refetch();\n        }\n    };\n    // Add this useEffect to get collegeId from multiple sources\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TeachersListPage.useEffect\": ()=>{\n            // Try to get collegeId from localStorage\n            const storedCollegeId = localStorage.getItem('collegeId');\n            if (storedCollegeId) {\n                console.log(\"Found collegeId in localStorage:\", storedCollegeId);\n                setCollegeId(storedCollegeId);\n                return;\n            }\n            // If not found in localStorage, try to extract from JWT token\n            try {\n                const possibleTokenKeys = [\n                    'token',\n                    'backendToken',\n                    'authToken',\n                    'jwtToken'\n                ];\n                for (const key of possibleTokenKeys){\n                    const token = localStorage.getItem(key);\n                    if (token) {\n                        try {\n                            const parts = token.split('.');\n                            if (parts.length === 3) {\n                                const payload = JSON.parse(atob(parts[1]));\n                                console.log(\"JWT payload:\", payload);\n                                if (payload.collegeId) {\n                                    console.log(\"Found collegeId in \".concat(key, \":\"), payload.collegeId);\n                                    setCollegeId(payload.collegeId);\n                                    // Store it in localStorage for future use\n                                    localStorage.setItem('collegeId', payload.collegeId);\n                                    return;\n                                }\n                            }\n                        } catch (e) {\n                            console.error(\"Error parsing token from \".concat(key, \":\"), e);\n                        }\n                    }\n                }\n                // Log all localStorage keys for debugging\n                console.log(\"All localStorage keys:\", Object.keys(localStorage));\n                console.error(\"Could not find collegeId in any token or localStorage\");\n            } catch (error) {\n                console.error('Error getting collegeId:', error);\n            }\n        }\n    }[\"TeachersListPage.useEffect\"], []);\n    // Fetch teachers data\n    const { data: teachersData = [], isLoading, refetch, error } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            'teachers',\n            collegeId,\n            currentPage,\n            pageSize,\n            filters\n        ],\n        queryFn: {\n            \"TeachersListPage.useQuery\": async ()=>{\n                if (!collegeId) {\n                    console.log(\"No collegeId found, returning empty array\");\n                    return [];\n                }\n                try {\n                    console.log(\"Fetching teachers for collegeId: \".concat(collegeId, \", page: \").concat(currentPage, \", limit: \").concat(pageSize));\n                    // Create filter object for API\n                    const apiFilters = {};\n                    if (filters.name) apiFilters.name = filters.name;\n                    if (filters.email) apiFilters.email = filters.email;\n                    if (filters.department && filters.department !== 'all_departments') {\n                        apiFilters.department = filters.department;\n                    }\n                    if (filters.status && filters.status !== 'all') {\n                        apiFilters.status = filters.status;\n                    }\n                    const response = await (0,_lib_api_teachers__WEBPACK_IMPORTED_MODULE_8__.getCollegeTeachers)(collegeId, currentPage, pageSize, apiFilters);\n                    console.log(\"API returned response:\", response);\n                    // Check if the API call was successful\n                    if (!(0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_9__.isApiSuccess)(response)) {\n                        // Error is already handled by the API function (toast shown)\n                        return [];\n                    }\n                    const data = response.data;\n                    // Handle both response formats (array or object with teachers property)\n                    let teachersArray = [];\n                    let total = 0;\n                    let totalPages = 1;\n                    if (Array.isArray(data)) {\n                        // API returned an array directly\n                        teachersArray = data;\n                        total = data.length;\n                        totalPages = 1;\n                    } else if (data && data.teachers) {\n                        // API returned an object with teachers property\n                        teachersArray = data.teachers;\n                        total = data.total || data.teachers.length;\n                        totalPages = data.totalPages || Math.ceil(total / pageSize);\n                    } else {\n                        console.error(\"API returned invalid data format:\", data);\n                        return [];\n                    }\n                    // Set total counts for pagination\n                    setTotalTeachers(total);\n                    setTotalPages(totalPages);\n                    // Transform the data to match the TeacherData interface\n                    const transformedData = teachersArray.map({\n                        \"TeachersListPage.useQuery.transformedData\": (teacher)=>{\n                            return {\n                                id: teacher._id,\n                                name: teacher.displayName || teacher.name || 'Unknown',\n                                email: teacher.email || 'No email',\n                                department: teacher.department || 'N/A',\n                                phone: teacher.phone || 'N/A',\n                                status: teacher.status === 'active' ? 'Active' : 'Inactive',\n                                avatar: teacher.avatar || null\n                            };\n                        }\n                    }[\"TeachersListPage.useQuery.transformedData\"]);\n                    return transformedData;\n                } catch (error) {\n                    console.error('Failed to fetch teachers:', error);\n                    sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('Failed to load teachers. Please try again.');\n                    return [];\n                }\n            }\n        }[\"TeachersListPage.useQuery\"],\n        enabled: !!collegeId,\n        staleTime: 0,\n        refetchOnWindowFocus: true\n    });\n    // Add debugging to see if there's an error\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TeachersListPage.useEffect\": ()=>{\n            if (error) {\n                console.error(\"Query error:\", error);\n            }\n        }\n    }[\"TeachersListPage.useEffect\"], [\n        error\n    ]);\n    // Extract unique departments for filter dropdown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TeachersListPage.useEffect\": ()=>{\n            console.log(\"teachersData changed:\", teachersData);\n            console.log(\"teachersData length:\", (teachersData === null || teachersData === void 0 ? void 0 : teachersData.length) || 0);\n            if (teachersData && teachersData.length > 0) {\n                const uniqueDepartments = Array.from(new Set(teachersData.map({\n                    \"TeachersListPage.useEffect.uniqueDepartments\": (teacher)=>teacher.department\n                }[\"TeachersListPage.useEffect.uniqueDepartments\"]).filter(Boolean)));\n                setDepartments(uniqueDepartments);\n                // Make sure we're setting the filtered teachers to the full data set initially\n                setFilteredTeachers([\n                    ...teachersData\n                ]);\n                console.log(\"Setting filtered teachers to:\", teachersData.length);\n            } else {\n                console.log(\"No teachers data to set\");\n            }\n        }\n    }[\"TeachersListPage.useEffect\"], [\n        teachersData\n    ]);\n    const handleTabChange = (tab)=>{\n        console.log('Page received tab change:', tab);\n        // If switching to edit mode from view, don't clear the editing teacher\n        if (tab !== 'edit') {\n            setEditingTeacher(null);\n        }\n        // Set the active tab with a small delay to allow for animation\n        setTimeout(()=>{\n            setActiveTab(tab);\n        }, 100);\n    };\n    const handleTeacherAdded = ()=>{\n        refetch(); // Refresh the teachers list\n        setActiveTab('view'); // Switch back to view tab\n    };\n    const handleEdit = (id)=>{\n        console.log(\"Edit teacher with id: \".concat(id));\n        const teacher = teachersData.find((t)=>t.id === id);\n        if (teacher) {\n            setEditingTeacher(teacher);\n            setActiveTab('edit');\n        }\n    };\n    const handleDelete = (id)=>{\n        console.log(\"Delete teacher with id: \".concat(id));\n        setTeacherToDelete(id);\n        setIsDeleteDialogOpen(true);\n    };\n    const confirmDelete = async ()=>{\n        if (!teacherToDelete) return;\n        try {\n            await (0,_lib_api_teachers__WEBPACK_IMPORTED_MODULE_8__.deleteTeacher)(teacherToDelete);\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success('Teacher deleted successfully');\n            refetch(); // Refresh the list\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error(error.message || 'Failed to delete teacher');\n        } finally{\n            setIsDeleteDialogOpen(false);\n            setTeacherToDelete(null);\n        }\n    };\n    const handleUpdateTeacher = async (formData)=>{\n        if (!editingTeacher) return;\n        try {\n            // Only send the allowed fields and ensure status is lowercase\n            const updateData = {\n                phone: formData.phone,\n                department: formData.department,\n                designation: formData.designation,\n                status: formData.status.toLowerCase()\n            };\n            await (0,_lib_api_teachers__WEBPACK_IMPORTED_MODULE_8__.updateTeacher)(editingTeacher.id, updateData);\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success('Teacher updated successfully');\n            refetch(); // Refresh the list\n            setActiveTab('view'); // Switch back to view tab\n            setEditingTeacher(null);\n        } catch (error) {\n            console.error('Failed to update teacher:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error(error.message || 'Failed to update teacher');\n        }\n    };\n    const handleOpenFilterModal = ()=>{\n        setIsFilterModalOpen(true);\n    };\n    const handleFilterApply = async (filterValues)=>{\n        // Update filter state used by React Query\n        setFilters(filterValues);\n        // Reset to page 1 whenever new filter is applied\n        setCurrentPage(1);\n        // Refetch teacher list using updated filters\n        if (collegeId) {\n            refetch();\n        }\n        // Optional: Inform user when default filter (i.e., all fields empty) is applied\n        const isEmptyFilter = !filterValues.name && !filterValues.email && (!filterValues.department || filterValues.department === 'all_departments') && (!filterValues.status || filterValues.status === 'all');\n        if (isEmptyFilter) {\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.info('Showing all teachers');\n        }\n    };\n    const handlePageChange = (page)=>{\n        setCurrentPage(page);\n    };\n    const handlePageSizeChange = (size)=>{\n        setPageSize(size);\n        setCurrentPage(1); // Reset to first page when changing page size\n    };\n    const paginatedTeachers = (filteredTeachers.length > 0 ? filteredTeachers : teachersData).slice((currentPage - 1) * pageSize, currentPage * pageSize);\n    const renderTeachersTable = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_table_teachers_table__WEBPACK_IMPORTED_MODULE_4__.TeachersTable, {\n                    data: paginatedTeachers,\n                    onEdit: handleEdit,\n                    onDelete: handleDelete,\n                    itemsPerPage: pageSize,\n                    isLoading: isLoading,\n                    onRefresh: ()=>{\n                        // Reset filters and show all teachers\n                        setFilters({\n                            name: '',\n                            department: '',\n                            status: 'all',\n                            email: ''\n                        });\n                        setFilteredTeachers([]);\n                        refetch();\n                    },\n                    onFilter: handleOpenFilterModal,\n                    columns: [\n                        \"name\",\n                        \"department\",\n                        \"email\",\n                        \"phone\",\n                        \"status\",\n                        \"actions\"\n                    ]\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                    lineNumber: 339,\n                    columnNumber: 9\n                }, undefined),\n                !isLoading && teachersData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_12__.Pagination, {\n                    currentPage: currentPage,\n                    totalPages: Math.ceil((filteredTeachers.length > 0 ? filteredTeachers.length : totalTeachers) / pageSize),\n                    pageSize: pageSize,\n                    totalItems: filteredTeachers.length > 0 ? filteredTeachers.length : totalTeachers,\n                    onPageChange: handlePageChange,\n                    onPageSizeChange: handlePageSizeChange,\n                    pageSizeOptions: [\n                        5,\n                        10,\n                        20,\n                        50\n                    ]\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                    lineNumber: 361,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n            lineNumber: 338,\n            columnNumber: 7\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container py-6\",\n        children: [\n            !collegeId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 p-4 border border-yellow-300 bg-yellow-50 rounded-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-yellow-800\",\n                            children: \"College ID not found. Please enter it manually or check your login status.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 13\n                        }, undefined),\n                        !showCollegeIdInput ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                            variant: \"outline\",\n                            onClick: ()=>setShowCollegeIdInput(true),\n                            children: \"Enter College ID Manually\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 15\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: manualCollegeId,\n                                    onChange: (e)=>setManualCollegeId(e.target.value),\n                                    placeholder: \"Enter College ID\",\n                                    className: \"px-3 py-2 border rounded-md flex-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    onClick: handleManualCollegeIdSubmit,\n                                    children: \"Submit\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowCollegeIdInput(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                            lineNumber: 393,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                    lineNumber: 381,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                lineNumber: 380,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold tracking-tight\",\n                                children: activeTab === 'view' ? \"Teacher's List View\" : activeTab === 'add' ? \"Add Teachers\" : activeTab === 'edit' ? \"Edit Teacher\" : \"Teacher Activity Logs\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Breadcrumb__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                items: [\n                                    {\n                                        label: 'Home',\n                                        href: '/'\n                                    },\n                                    {\n                                        label: '...',\n                                        href: '#'\n                                    },\n                                    {\n                                        label: activeTab === 'view' ? 'Teacher list' : activeTab === 'add' ? 'Add teachers' : activeTab === 'edit' ? 'Edit teacher' : 'Teacher activity logs'\n                                    }\n                                ],\n                                className: \"mt-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                        lineNumber: 416,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"rounded-lg border bg-card text-card-foreground shadow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center px-6 pt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TeacherList_Tabs__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    activeTab: activeTab === 'edit' ? 'view' : activeTab,\n                                    onTabChange: handleTabChange,\n                                    collegeIdMissing: !collegeId\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 11\n                            }, undefined),\n                            activeTab === 'view' && renderTeachersTable(),\n                            activeTab === 'add' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TeacherList_AddTeacherForm__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    onCancel: ()=>handleTabChange('view'),\n                                    onSuccess: handleTeacherAdded\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                                    lineNumber: 464,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                                lineNumber: 463,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === 'edit' && editingTeacher && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TeacherList_EditTeacherForm__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    teacher: editingTeacher,\n                                    onCancel: ()=>handleTabChange('view'),\n                                    onSubmit: handleUpdateTeacher\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                                lineNumber: 472,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                        lineNumber: 436,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                lineNumber: 415,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                open: isDeleteDialogOpen,\n                onOpenChange: setIsDeleteDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                    children: \"Confirm Deletion\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogDescription, {\n                                    children: \"Are you sure you want to delete this teacher? This action cannot be undone.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                            lineNumber: 494,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-4 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsDeleteDialogOpen(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                                    lineNumber: 501,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    variant: \"destructive\",\n                                    onClick: confirmDelete,\n                                    children: \"Delete\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                            lineNumber: 500,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                    lineNumber: 493,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                lineNumber: 492,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n        lineNumber: 378,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TeachersListPage, \"G2atMfKpCTCQtusi8mdNkpyUlu4=\", false, function() {\n    return [\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery\n    ];\n});\n_c = TeachersListPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TeachersListPage);\nvar _c;\n$RefreshReg$(_c, \"TeachersListPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvY29sbGVnZS90ZWFjaGVycy1saXN0L3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUNtRDtBQUNGO0FBQ0E7QUFDbEI7QUFDbUM7QUFFVjtBQUNhO0FBQ0U7QUFHZTtBQUM5QjtBQUVxRDtBQUM3RDtBQUVRO0FBRXhELE1BQU1xQixtQkFBNkI7O0lBQ2pDLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHdEIsK0NBQVFBLENBQVM7SUFDbkQsTUFBTSxDQUFDdUIsZ0JBQWdCQyxrQkFBa0IsR0FBR3hCLCtDQUFRQSxDQUFxQjtJQUN6RSxNQUFNLENBQUN5QixvQkFBb0JDLHNCQUFzQixHQUFHMUIsK0NBQVFBLENBQUM7SUFDN0QsTUFBTSxDQUFDMkIsaUJBQWlCQyxtQkFBbUIsR0FBRzVCLCtDQUFRQSxDQUFnQjtJQUN0RSxNQUFNLENBQUM2QixtQkFBbUJDLHFCQUFxQixHQUFHOUIsK0NBQVFBLENBQUM7SUFDM0QsTUFBTSxDQUFDK0IsU0FBU0MsV0FBVyxHQUFHaEMsK0NBQVFBLENBQXNCO1FBQzFEaUMsTUFBTTtRQUNOQyxZQUFZO1FBQ1pDLFFBQVE7UUFDUkMsT0FBTztJQUNUO0lBQ0EsTUFBTSxDQUFDQyxhQUFhQyxlQUFlLEdBQUd0QywrQ0FBUUEsQ0FBVyxFQUFFO0lBQzNELE1BQU0sQ0FBQ3VDLGtCQUFrQkMsb0JBQW9CLEdBQUd4QywrQ0FBUUEsQ0FBZ0IsRUFBRTtJQUMxRSxNQUFNLENBQUN5QyxXQUFXQyxhQUFhLEdBQUcxQywrQ0FBUUEsQ0FBZ0I7SUFDMUQsTUFBTSxDQUFDMkMsb0JBQW9CQyxzQkFBc0IsR0FBRzVDLCtDQUFRQSxDQUFDO0lBQzdELE1BQU0sQ0FBQzZDLGlCQUFpQkMsbUJBQW1CLEdBQUc5QywrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNLENBQUMrQyxhQUFhQyxlQUFlLEdBQUdoRCwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNpRCxVQUFVQyxZQUFZLEdBQUdsRCwrQ0FBUUEsQ0FBQztJQUN6QyxNQUFNLENBQUNtRCxlQUFlQyxpQkFBaUIsR0FBR3BELCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQ3FELFlBQVlDLGNBQWMsR0FBR3RELCtDQUFRQSxDQUFDO0lBRTdDLHVEQUF1RDtJQUN2RCxNQUFNdUQsOEJBQThCO1FBQ2xDLElBQUlWLGdCQUFnQlcsSUFBSSxJQUFJO1lBQzFCQyxRQUFRQyxHQUFHLENBQUMsNkJBQTZCYjtZQUN6Q0gsYUFBYUc7WUFDYmMsYUFBYUMsT0FBTyxDQUFDLGFBQWFmO1lBQ2xDRCxzQkFBc0I7WUFDdEJpQjtRQUNGO0lBQ0Y7SUFFQSw0REFBNEQ7SUFDNUQ1RCxnREFBU0E7c0NBQUM7WUFDUix5Q0FBeUM7WUFDekMsTUFBTTZELGtCQUFrQkgsYUFBYUksT0FBTyxDQUFDO1lBRTdDLElBQUlELGlCQUFpQjtnQkFDbkJMLFFBQVFDLEdBQUcsQ0FBQyxvQ0FBb0NJO2dCQUNoRHBCLGFBQWFvQjtnQkFDYjtZQUNGO1lBRUEsOERBQThEO1lBQzlELElBQUk7Z0JBQ0YsTUFBTUUsb0JBQW9CO29CQUFDO29CQUFTO29CQUFnQjtvQkFBYTtpQkFBVztnQkFFNUUsS0FBSyxNQUFNQyxPQUFPRCxrQkFBbUI7b0JBQ25DLE1BQU1FLFFBQVFQLGFBQWFJLE9BQU8sQ0FBQ0U7b0JBQ25DLElBQUlDLE9BQU87d0JBQ1QsSUFBSTs0QkFDRixNQUFNQyxRQUFRRCxNQUFNRSxLQUFLLENBQUM7NEJBQzFCLElBQUlELE1BQU1FLE1BQU0sS0FBSyxHQUFHO2dDQUN0QixNQUFNQyxVQUFVQyxLQUFLQyxLQUFLLENBQUNDLEtBQUtOLEtBQUssQ0FBQyxFQUFFO2dDQUN4Q1YsUUFBUUMsR0FBRyxDQUFDLGdCQUFnQlk7Z0NBRTVCLElBQUlBLFFBQVE3QixTQUFTLEVBQUU7b0NBQ3JCZ0IsUUFBUUMsR0FBRyxDQUFDLHNCQUEwQixPQUFKTyxLQUFJLE1BQUlLLFFBQVE3QixTQUFTO29DQUMzREMsYUFBYTRCLFFBQVE3QixTQUFTO29DQUM5QiwwQ0FBMEM7b0NBQzFDa0IsYUFBYUMsT0FBTyxDQUFDLGFBQWFVLFFBQVE3QixTQUFTO29DQUNuRDtnQ0FDRjs0QkFDRjt3QkFDRixFQUFFLE9BQU9pQyxHQUFHOzRCQUNWakIsUUFBUWtCLEtBQUssQ0FBQyw0QkFBZ0MsT0FBSlYsS0FBSSxNQUFJUzt3QkFDcEQ7b0JBQ0Y7Z0JBQ0Y7Z0JBRUEsMENBQTBDO2dCQUMxQ2pCLFFBQVFDLEdBQUcsQ0FBQywwQkFBMEJrQixPQUFPQyxJQUFJLENBQUNsQjtnQkFDbERGLFFBQVFrQixLQUFLLENBQUM7WUFDaEIsRUFBRSxPQUFPQSxPQUFPO2dCQUNkbEIsUUFBUWtCLEtBQUssQ0FBQyw0QkFBNEJBO1lBQzVDO1FBQ0Y7cUNBQUcsRUFBRTtJQUVMLHNCQUFzQjtJQUN0QixNQUFNLEVBQ0pHLE1BQU1DLGVBQWUsRUFBRSxFQUN2QkMsU0FBUyxFQUNUbkIsT0FBTyxFQUNQYyxLQUFLLEVBQ04sR0FBR3pFLGdFQUFRQSxDQUFDO1FBQ1grRSxVQUFVO1lBQUM7WUFBWXhDO1lBQVdNO1lBQWFFO1lBQVVsQjtTQUFRO1FBQ2pFbUQsT0FBTzt5Q0FBRTtnQkFDUCxJQUFJLENBQUN6QyxXQUFXO29CQUNkZ0IsUUFBUUMsR0FBRyxDQUFDO29CQUNaLE9BQU8sRUFBRTtnQkFDWDtnQkFFQSxJQUFJO29CQUNGRCxRQUFRQyxHQUFHLENBQUMsb0NBQXdEWCxPQUFwQk4sV0FBVSxZQUFpQ1EsT0FBdkJGLGFBQVksYUFBb0IsT0FBVEU7b0JBRTNGLCtCQUErQjtvQkFDL0IsTUFBTWtDLGFBQXFDLENBQUM7b0JBQzVDLElBQUlwRCxRQUFRRSxJQUFJLEVBQUVrRCxXQUFXbEQsSUFBSSxHQUFHRixRQUFRRSxJQUFJO29CQUNoRCxJQUFJRixRQUFRSyxLQUFLLEVBQUUrQyxXQUFXL0MsS0FBSyxHQUFHTCxRQUFRSyxLQUFLO29CQUNuRCxJQUFJTCxRQUFRRyxVQUFVLElBQUlILFFBQVFHLFVBQVUsS0FBSyxtQkFBbUI7d0JBQ2xFaUQsV0FBV2pELFVBQVUsR0FBR0gsUUFBUUcsVUFBVTtvQkFDNUM7b0JBQ0EsSUFBSUgsUUFBUUksTUFBTSxJQUFJSixRQUFRSSxNQUFNLEtBQUssT0FBTzt3QkFDOUNnRCxXQUFXaEQsTUFBTSxHQUFHSixRQUFRSSxNQUFNO29CQUNwQztvQkFFQSxNQUFNaUQsV0FBVyxNQUFNM0UscUVBQWtCQSxDQUFDZ0MsV0FBV00sYUFBYUUsVUFBVWtDO29CQUU1RTFCLFFBQVFDLEdBQUcsQ0FBQywwQkFBMEIwQjtvQkFFdEMsdUNBQXVDO29CQUN2QyxJQUFJLENBQUN4RSxxRUFBWUEsQ0FBQ3dFLFdBQVc7d0JBQzNCLDZEQUE2RDt3QkFDN0QsT0FBTyxFQUFFO29CQUNYO29CQUVBLE1BQU1OLE9BQU9NLFNBQVNOLElBQUk7b0JBRTFCLHdFQUF3RTtvQkFDeEUsSUFBSU8sZ0JBQWdCLEVBQUU7b0JBQ3RCLElBQUlDLFFBQVE7b0JBQ1osSUFBSWpDLGFBQWE7b0JBRWpCLElBQUlrQyxNQUFNQyxPQUFPLENBQUNWLE9BQU87d0JBQ3ZCLGlDQUFpQzt3QkFDakNPLGdCQUFnQlA7d0JBQ2hCUSxRQUFRUixLQUFLVCxNQUFNO3dCQUNuQmhCLGFBQWE7b0JBQ2YsT0FBTyxJQUFJeUIsUUFBUUEsS0FBS1csUUFBUSxFQUFFO3dCQUNoQyxnREFBZ0Q7d0JBQ2hESixnQkFBZ0JQLEtBQUtXLFFBQVE7d0JBQzdCSCxRQUFRUixLQUFLUSxLQUFLLElBQUlSLEtBQUtXLFFBQVEsQ0FBQ3BCLE1BQU07d0JBQzFDaEIsYUFBYXlCLEtBQUt6QixVQUFVLElBQUlxQyxLQUFLQyxJQUFJLENBQUNMLFFBQVFyQztvQkFDcEQsT0FBTzt3QkFDTFEsUUFBUWtCLEtBQUssQ0FBQyxxQ0FBcUNHO3dCQUNuRCxPQUFPLEVBQUU7b0JBQ1g7b0JBRUEsa0NBQWtDO29CQUNsQzFCLGlCQUFpQmtDO29CQUNqQmhDLGNBQWNEO29CQUVkLHdEQUF3RDtvQkFDeEQsTUFBTXVDLGtCQUFrQlAsY0FBY1EsR0FBRztxRUFBQyxDQUFDQzs0QkFDekMsT0FBTztnQ0FDTEMsSUFBSUQsUUFBUUUsR0FBRztnQ0FDZi9ELE1BQU02RCxRQUFRRyxXQUFXLElBQUlILFFBQVE3RCxJQUFJLElBQUk7Z0NBQzdDRyxPQUFPMEQsUUFBUTFELEtBQUssSUFBSTtnQ0FDeEJGLFlBQVk0RCxRQUFRNUQsVUFBVSxJQUFJO2dDQUNsQ2dFLE9BQU9KLFFBQVFJLEtBQUssSUFBSTtnQ0FDeEIvRCxRQUFRMkQsUUFBUTNELE1BQU0sS0FBSyxXQUFXLFdBQVc7Z0NBQ2pEZ0UsUUFBUUwsUUFBUUssTUFBTSxJQUFJOzRCQUM1Qjt3QkFDRjs7b0JBRUEsT0FBT1A7Z0JBQ1QsRUFBRSxPQUFPakIsT0FBTztvQkFDZGxCLFFBQVFrQixLQUFLLENBQUMsNkJBQTZCQTtvQkFDM0N2RSx5Q0FBS0EsQ0FBQ3VFLEtBQUssQ0FBQztvQkFDWixPQUFPLEVBQUU7Z0JBQ1g7WUFDRjs7UUFDQXlCLFNBQVMsQ0FBQyxDQUFDM0Q7UUFDWDRELFdBQVc7UUFDWEMsc0JBQXNCO0lBQ3hCO0lBRUEsMkNBQTJDO0lBQzNDckcsZ0RBQVNBO3NDQUFDO1lBQ1IsSUFBSTBFLE9BQU87Z0JBQ1RsQixRQUFRa0IsS0FBSyxDQUFDLGdCQUFnQkE7WUFDaEM7UUFDRjtxQ0FBRztRQUFDQTtLQUFNO0lBRVYsaURBQWlEO0lBQ2pEMUUsZ0RBQVNBO3NDQUFDO1lBQ1J3RCxRQUFRQyxHQUFHLENBQUMseUJBQXlCcUI7WUFDckN0QixRQUFRQyxHQUFHLENBQUMsd0JBQXdCcUIsQ0FBQUEseUJBQUFBLG1DQUFBQSxhQUFjVixNQUFNLEtBQUk7WUFFNUQsSUFBSVUsZ0JBQWdCQSxhQUFhVixNQUFNLEdBQUcsR0FBRztnQkFLM0MsTUFBTWtDLG9CQUE4QmhCLE1BQU1pQixJQUFJLENBQzVDLElBQUlDLElBQ0YsYUFDRFosR0FBRztvRUFBQyxDQUFDQyxVQUF5QkEsUUFBUTVELFVBQVU7bUVBQ2hEd0UsTUFBTSxDQUFDQztnQkFHVnJFLGVBQWVpRTtnQkFFZiwrRUFBK0U7Z0JBQy9FL0Qsb0JBQW9CO3VCQUFJdUM7aUJBQWE7Z0JBQ3JDdEIsUUFBUUMsR0FBRyxDQUFDLGlDQUFpQ3FCLGFBQWFWLE1BQU07WUFDbEUsT0FBTztnQkFDTFosUUFBUUMsR0FBRyxDQUFDO1lBQ2Q7UUFDRjtxQ0FBRztRQUFDcUI7S0FBYTtJQUVqQixNQUFNNkIsa0JBQWtCLENBQUNDO1FBQ3ZCcEQsUUFBUUMsR0FBRyxDQUFDLDZCQUE2Qm1EO1FBRXpDLHVFQUF1RTtRQUN2RSxJQUFJQSxRQUFRLFFBQVE7WUFDbEJyRixrQkFBa0I7UUFDcEI7UUFFQSwrREFBK0Q7UUFDL0RzRixXQUFXO1lBQ1R4RixhQUFhdUY7UUFDZixHQUFHO0lBQ0w7SUFFQSxNQUFNRSxxQkFBcUI7UUFDekJsRCxXQUFXLDRCQUE0QjtRQUN2Q3ZDLGFBQWEsU0FBUywwQkFBMEI7SUFDbEQ7SUFFQSxNQUFNMEYsYUFBYSxDQUFDakI7UUFDbEJ0QyxRQUFRQyxHQUFHLENBQUMseUJBQTRCLE9BQUhxQztRQUNyQyxNQUFNRCxVQUFVZixhQUFha0MsSUFBSSxDQUFDLENBQUNDLElBQW1CQSxFQUFFbkIsRUFBRSxLQUFLQTtRQUMvRCxJQUFJRCxTQUFTO1lBQ1h0RSxrQkFBa0JzRTtZQUNsQnhFLGFBQWE7UUFDZjtJQUNGO0lBRUEsTUFBTTZGLGVBQWUsQ0FBQ3BCO1FBQ3BCdEMsUUFBUUMsR0FBRyxDQUFDLDJCQUE4QixPQUFIcUM7UUFDdkNuRSxtQkFBbUJtRTtRQUNuQnJFLHNCQUFzQjtJQUN4QjtJQUVBLE1BQU0wRixnQkFBZ0I7UUFDcEIsSUFBSSxDQUFDekYsaUJBQWlCO1FBRXRCLElBQUk7WUFDRixNQUFNakIsZ0VBQWFBLENBQUNpQjtZQUNwQnZCLHlDQUFLQSxDQUFDaUgsT0FBTyxDQUFDO1lBQ2R4RCxXQUFXLG1CQUFtQjtRQUNoQyxFQUFFLE9BQU9jLE9BQVk7WUFDbkJ2RSx5Q0FBS0EsQ0FBQ3VFLEtBQUssQ0FBQ0EsTUFBTTJDLE9BQU8sSUFBSTtRQUMvQixTQUFVO1lBQ1I1RixzQkFBc0I7WUFDdEJFLG1CQUFtQjtRQUNyQjtJQUNGO0lBRUEsTUFBTTJGLHNCQUFzQixPQUFPQztRQUNqQyxJQUFJLENBQUNqRyxnQkFBZ0I7UUFFckIsSUFBSTtZQUNGLDhEQUE4RDtZQUM5RCxNQUFNa0csYUFBYTtnQkFDakJ2QixPQUFPc0IsU0FBU3RCLEtBQUs7Z0JBQ3JCaEUsWUFBWXNGLFNBQVN0RixVQUFVO2dCQUMvQndGLGFBQWFGLFNBQVNFLFdBQVc7Z0JBQ2pDdkYsUUFBUXFGLFNBQVNyRixNQUFNLENBQUN3RixXQUFXO1lBQ3JDO1lBRUEsTUFBTWhILGdFQUFhQSxDQUFDWSxlQUFld0UsRUFBRSxFQUFFMEI7WUFDdkNySCx5Q0FBS0EsQ0FBQ2lILE9BQU8sQ0FBQztZQUNkeEQsV0FBVyxtQkFBbUI7WUFDOUJ2QyxhQUFhLFNBQVMsMEJBQTBCO1lBQ2hERSxrQkFBa0I7UUFDcEIsRUFBRSxPQUFPbUQsT0FBWTtZQUNuQmxCLFFBQVFrQixLQUFLLENBQUMsNkJBQTZCQTtZQUMzQ3ZFLHlDQUFLQSxDQUFDdUUsS0FBSyxDQUFDQSxNQUFNMkMsT0FBTyxJQUFJO1FBQy9CO0lBQ0Y7SUFFQSxNQUFNTSx3QkFBd0I7UUFDNUI5RixxQkFBcUI7SUFDdkI7SUFDRixNQUFNK0Ysb0JBQW9CLE9BQU9DO1FBQy9CLDBDQUEwQztRQUMxQzlGLFdBQVc4RjtRQUNYLGlEQUFpRDtRQUNqRDlFLGVBQWU7UUFFZiw2Q0FBNkM7UUFDN0MsSUFBSVAsV0FBVztZQUNib0I7UUFDRjtRQUVBLGdGQUFnRjtRQUNoRixNQUFNa0UsZ0JBQ0osQ0FBQ0QsYUFBYTdGLElBQUksSUFDbEIsQ0FBQzZGLGFBQWExRixLQUFLLElBQ2xCLEVBQUMwRixhQUFhNUYsVUFBVSxJQUFJNEYsYUFBYTVGLFVBQVUsS0FBSyxpQkFBZ0IsS0FDeEUsRUFBQzRGLGFBQWEzRixNQUFNLElBQUkyRixhQUFhM0YsTUFBTSxLQUFLLEtBQUk7UUFFdkQsSUFBSTRGLGVBQWU7WUFDakIzSCx5Q0FBS0EsQ0FBQzRILElBQUksQ0FBQztRQUNiO0lBQ0Y7SUFHRSxNQUFNQyxtQkFBbUIsQ0FBQ0M7UUFDeEJsRixlQUFla0Y7SUFDakI7SUFFQSxNQUFNQyx1QkFBdUIsQ0FBQ0M7UUFDNUJsRixZQUFZa0Y7UUFDWnBGLGVBQWUsSUFBSSw4Q0FBOEM7SUFDbkU7SUFFQSxNQUFNcUYsb0JBQW9CLENBQUM5RixpQkFBaUI4QixNQUFNLEdBQUcsSUFBSTlCLG1CQUFtQndDLFlBQVcsRUFBR3VELEtBQUssQ0FDL0YsQ0FBQ3ZGLGNBQWMsS0FBS0UsVUFDcEJGLGNBQWNFO0lBR2QsTUFBTXNGLHNCQUFzQjtRQUMxQixxQkFDRSw4REFBQ0M7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNwSSwyRUFBYUE7b0JBQ1p5RSxNQUFNdUQ7b0JBQ05LLFFBQVExQjtvQkFDUjJCLFVBQVV4QjtvQkFDVnlCLGNBQWMzRjtvQkFDZCtCLFdBQVdBO29CQUNYNkQsV0FBVzt3QkFDVCxzQ0FBc0M7d0JBQ3RDN0csV0FBVzs0QkFDVEMsTUFBTTs0QkFDTkMsWUFBWTs0QkFDWkMsUUFBUTs0QkFDUkMsT0FBTzt3QkFDVDt3QkFDQUksb0JBQW9CLEVBQUU7d0JBQ3RCcUI7b0JBQ0Y7b0JBQ0FpRixVQUFVbEI7b0JBQ1ZtQixTQUFTO3dCQUFDO3dCQUFRO3dCQUFjO3dCQUFTO3dCQUFTO3dCQUFVO3FCQUFVOzs7Ozs7Z0JBR3ZFLENBQUMvRCxhQUFhRCxhQUFhVixNQUFNLEdBQUcsbUJBQ25DLDhEQUFDbEQsa0VBQVVBO29CQUNuQjRCLGFBQWFBO29CQUNiTSxZQUFZcUMsS0FBS0MsSUFBSSxDQUNuQixDQUFDcEQsaUJBQWlCOEIsTUFBTSxHQUFHLElBQUk5QixpQkFBaUI4QixNQUFNLEdBQUdsQixhQUFZLElBQUtGO29CQUU1RUEsVUFBVUE7b0JBQ1YrRixZQUFZekcsaUJBQWlCOEIsTUFBTSxHQUFHLElBQUk5QixpQkFBaUI4QixNQUFNLEdBQUdsQjtvQkFDcEU4RixjQUFjaEI7b0JBQ2RpQixrQkFBa0JmO29CQUNsQmdCLGlCQUFpQjt3QkFBQzt3QkFBRzt3QkFBSTt3QkFBSTtxQkFBRzs7Ozs7Ozs7Ozs7O0lBS2hDO0lBRUEscUJBQ0UsOERBQUNYO1FBQUlDLFdBQVU7O1lBQ1osQ0FBQ2hHLDJCQUNBLDhEQUFDK0Y7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ1c7NEJBQUVYLFdBQVU7c0NBQWtCOzs7Ozs7d0JBRzlCLENBQUM5RixtQ0FDQSw4REFBQ3pCLDBEQUFNQTs0QkFDTG1JLFNBQVE7NEJBQ1JDLFNBQVMsSUFBTTFHLHNCQUFzQjtzQ0FDdEM7Ozs7O3NEQUlELDhEQUFDNEY7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDYztvQ0FDQ0MsTUFBSztvQ0FDTEMsT0FBTzVHO29DQUNQNkcsVUFBVSxDQUFDaEYsSUFBTTVCLG1CQUFtQjRCLEVBQUVpRixNQUFNLENBQUNGLEtBQUs7b0NBQ2xERyxhQUFZO29DQUNabkIsV0FBVTs7Ozs7OzhDQUVaLDhEQUFDdkgsMERBQU1BO29DQUFDb0ksU0FBUy9GOzhDQUE2Qjs7Ozs7OzhDQUc5Qyw4REFBQ3JDLDBEQUFNQTtvQ0FDTG1JLFNBQVE7b0NBQ1JDLFNBQVMsSUFBTTFHLHNCQUFzQjs4Q0FDdEM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVFYLDhEQUFDNEY7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDs7MENBQ0MsOERBQUNxQjtnQ0FBR3BCLFdBQVU7MENBQ1hwSCxjQUFjLFNBQVMsd0JBQ3ZCQSxjQUFjLFFBQVEsaUJBQ3RCQSxjQUFjLFNBQVMsaUJBQ3ZCOzs7Ozs7MENBRUgsOERBQUNsQiw4REFBVUE7Z0NBQ1QySixPQUFPO29DQUNMO3dDQUFFQyxPQUFPO3dDQUFRQyxNQUFNO29DQUFJO29DQUMzQjt3Q0FBRUQsT0FBTzt3Q0FBT0MsTUFBTTtvQ0FBSTtvQ0FDMUI7d0NBQUVELE9BQU8xSSxjQUFjLFNBQVMsaUJBQ3hCQSxjQUFjLFFBQVEsaUJBQ3RCQSxjQUFjLFNBQVMsaUJBQ3ZCO29DQUF3QjtpQ0FDakM7Z0NBQ0RvSCxXQUFVOzs7Ozs7Ozs7Ozs7a0NBSWQsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNuSSxvRUFBV0E7b0NBQ1ZlLFdBQVdBLGNBQWMsU0FBUyxTQUFTQTtvQ0FDM0M0SSxhQUFhckQ7b0NBQ2JzRCxrQkFBa0IsQ0FBQ3pIOzs7Ozs7Ozs7Ozs0QkFtQnRCcEIsY0FBYyxVQUFVa0g7NEJBRXhCbEgsY0FBYyx1QkFDYiw4REFBQ21IO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDbEksOEVBQWNBO29DQUNiNEosVUFBVSxJQUFNdkQsZ0JBQWdCO29DQUNoQ3dELFdBQVdyRDs7Ozs7Ozs7Ozs7NEJBS2hCMUYsY0FBYyxVQUFVRSxnQ0FDdkIsOERBQUNpSDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ2pJLCtFQUFlQTtvQ0FDZHNGLFNBQVN2RTtvQ0FDVDRJLFVBQVUsSUFBTXZELGdCQUFnQjtvQ0FDaEN5RCxVQUFVOUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQWdCcEIsOERBQUMxRywwREFBTUE7Z0JBQUN5SixNQUFNN0k7Z0JBQW9COEksY0FBYzdJOzBCQUM5Qyw0RUFBQ1osaUVBQWFBOztzQ0FDWiw4REFBQ0MsZ0VBQVlBOzs4Q0FDWCw4REFBQ0MsK0RBQVdBOzhDQUFDOzs7Ozs7OENBQ2IsOERBQUNDLHFFQUFpQkE7OENBQUM7Ozs7Ozs7Ozs7OztzQ0FJckIsOERBQUN1SDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUN2SCwwREFBTUE7b0NBQUNtSSxTQUFRO29DQUFVQyxTQUFTLElBQU01SCxzQkFBc0I7OENBQVE7Ozs7Ozs4Q0FHdkUsOERBQUNSLDBEQUFNQTtvQ0FBQ21JLFNBQVE7b0NBQWNDLFNBQVNsQzs4Q0FBZTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFnQmxFO0dBbmZNaEc7O1FBcUZBbEIsNERBQVFBOzs7S0FyRlJrQjtBQXFmTixpRUFBZUEsZ0JBQWdCQSxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFkYXJzXFxEZXNrdG9wXFxGTFxcbWVkaWNvc1xcbWVkaWNvcy1mcm9udGVuZFxcc3JjXFxhcHBcXGNvbGxlZ2VcXHRlYWNoZXJzLWxpc3RcXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXHJcbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgeyB1c2VRdWVyeSB9IGZyb20gJ0B0YW5zdGFjay9yZWFjdC1xdWVyeSc7XHJcbmltcG9ydCBCcmVhZGNydW1iIGZyb20gJ0AvY29tcG9uZW50cy9CcmVhZGNydW1iJztcclxuaW1wb3J0IHsgdG9hc3QgfSBmcm9tICdzb25uZXInO1xyXG5pbXBvcnQgeyBUZWFjaGVyc1RhYmxlIH0gZnJvbSAnQC9jb21wb25lbnRzL3RhYmxlL3RlYWNoZXJzLXRhYmxlJztcclxuaW1wb3J0IHsgVGVhY2hlckRhdGEgfSBmcm9tICdAL2NvbXBvbmVudHMvdGFibGUvdHlwZXMnO1xyXG5pbXBvcnQgVGVhY2hlclRhYnMgZnJvbSAnQC9jb21wb25lbnRzL1RlYWNoZXJMaXN0L1RhYnMnO1xyXG5pbXBvcnQgQWRkVGVhY2hlckZvcm0gZnJvbSAnQC9jb21wb25lbnRzL1RlYWNoZXJMaXN0L0FkZFRlYWNoZXJGb3JtJztcclxuaW1wb3J0IEVkaXRUZWFjaGVyRm9ybSBmcm9tICdAL2NvbXBvbmVudHMvVGVhY2hlckxpc3QvRWRpdFRlYWNoZXJGb3JtJztcclxuaW1wb3J0IEZpbHRlck1vZGFsIGZyb20gJ0AvY29tcG9uZW50cy9UZWFjaGVyTGlzdC9GaWx0ZXJNb2RhbCc7XHJcbmltcG9ydCB7IFRlYWNoZXJGaWx0ZXJWYWx1ZXMgfSBmcm9tICdAL2NvbXBvbmVudHMvVGVhY2hlckxpc3QvRmlsdGVyTW9kYWwnO1xyXG5pbXBvcnQgeyBnZXRDb2xsZWdlVGVhY2hlcnMsIGRlbGV0ZVRlYWNoZXIsIHVwZGF0ZVRlYWNoZXIgfSBmcm9tICdAL2xpYi9hcGkvdGVhY2hlcnMnO1xyXG5pbXBvcnQgeyBpc0FwaVN1Y2Nlc3MgfSBmcm9tICdAL2xpYi91dGlscy9lcnJvckhhbmRsZXInO1xyXG5pbXBvcnQgeyBmaWx0ZXJUZWFjaGVycyB9IGZyb20gJ0Avc2VydmljZXMvdGVhY2hlclNlcnZpY2UnO1xyXG5pbXBvcnQgeyBEaWFsb2csIERpYWxvZ0NvbnRlbnQsIERpYWxvZ0hlYWRlciwgRGlhbG9nVGl0bGUsIERpYWxvZ0Rlc2NyaXB0aW9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2RpYWxvZyc7XHJcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9idXR0b24nO1xyXG5pbXBvcnQgeyBSZWZyZXNoQ3cgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xyXG5pbXBvcnQgeyBQYWdpbmF0aW9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3BhZ2luYXRpb24nO1xyXG5cclxuY29uc3QgVGVhY2hlcnNMaXN0UGFnZTogUmVhY3QuRkMgPSAoKSA9PiB7XHJcbiAgY29uc3QgW2FjdGl2ZVRhYiwgc2V0QWN0aXZlVGFiXSA9IHVzZVN0YXRlPHN0cmluZz4oJ3ZpZXcnKTtcclxuICBjb25zdCBbZWRpdGluZ1RlYWNoZXIsIHNldEVkaXRpbmdUZWFjaGVyXSA9IHVzZVN0YXRlPFRlYWNoZXJEYXRhIHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3QgW2lzRGVsZXRlRGlhbG9nT3Blbiwgc2V0SXNEZWxldGVEaWFsb2dPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbdGVhY2hlclRvRGVsZXRlLCBzZXRUZWFjaGVyVG9EZWxldGVdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3QgW2lzRmlsdGVyTW9kYWxPcGVuLCBzZXRJc0ZpbHRlck1vZGFsT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2ZpbHRlcnMsIHNldEZpbHRlcnNdID0gdXNlU3RhdGU8VGVhY2hlckZpbHRlclZhbHVlcz4oe1xyXG4gICAgbmFtZTogJycsXHJcbiAgICBkZXBhcnRtZW50OiAnJyxcclxuICAgIHN0YXR1czogJ2FsbCcsXHJcbiAgICBlbWFpbDogJydcclxuICB9KTtcclxuICBjb25zdCBbZGVwYXJ0bWVudHMsIHNldERlcGFydG1lbnRzXSA9IHVzZVN0YXRlPHN0cmluZ1tdPihbXSk7XHJcbiAgY29uc3QgW2ZpbHRlcmVkVGVhY2hlcnMsIHNldEZpbHRlcmVkVGVhY2hlcnNdID0gdXNlU3RhdGU8VGVhY2hlckRhdGFbXT4oW10pO1xyXG4gIGNvbnN0IFtjb2xsZWdlSWQsIHNldENvbGxlZ2VJZF0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBbc2hvd0NvbGxlZ2VJZElucHV0LCBzZXRTaG93Q29sbGVnZUlkSW5wdXRdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFttYW51YWxDb2xsZWdlSWQsIHNldE1hbnVhbENvbGxlZ2VJZF0gPSB1c2VTdGF0ZSgnJyk7XHJcbiAgY29uc3QgW2N1cnJlbnRQYWdlLCBzZXRDdXJyZW50UGFnZV0gPSB1c2VTdGF0ZSgxKTtcclxuICBjb25zdCBbcGFnZVNpemUsIHNldFBhZ2VTaXplXSA9IHVzZVN0YXRlKDEwKTtcclxuICBjb25zdCBbdG90YWxUZWFjaGVycywgc2V0VG90YWxUZWFjaGVyc10gPSB1c2VTdGF0ZSgwKTtcclxuICBjb25zdCBbdG90YWxQYWdlcywgc2V0VG90YWxQYWdlc10gPSB1c2VTdGF0ZSgxKTtcclxuXHJcbiAgLy8gQWRkIHRoaXMgZnVuY3Rpb24gdG8gaGFuZGxlIG1hbnVhbCBjb2xsZWdlSWQgc2V0dGluZ1xyXG4gIGNvbnN0IGhhbmRsZU1hbnVhbENvbGxlZ2VJZFN1Ym1pdCA9ICgpID0+IHtcclxuICAgIGlmIChtYW51YWxDb2xsZWdlSWQudHJpbSgpKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKFwiU2V0dGluZyBtYW51YWwgY29sbGVnZUlkOlwiLCBtYW51YWxDb2xsZWdlSWQpO1xyXG4gICAgICBzZXRDb2xsZWdlSWQobWFudWFsQ29sbGVnZUlkKTtcclxuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2NvbGxlZ2VJZCcsIG1hbnVhbENvbGxlZ2VJZCk7XHJcbiAgICAgIHNldFNob3dDb2xsZWdlSWRJbnB1dChmYWxzZSk7XHJcbiAgICAgIHJlZmV0Y2goKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyBBZGQgdGhpcyB1c2VFZmZlY3QgdG8gZ2V0IGNvbGxlZ2VJZCBmcm9tIG11bHRpcGxlIHNvdXJjZXNcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgLy8gVHJ5IHRvIGdldCBjb2xsZWdlSWQgZnJvbSBsb2NhbFN0b3JhZ2VcclxuICAgIGNvbnN0IHN0b3JlZENvbGxlZ2VJZCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdjb2xsZWdlSWQnKTtcclxuICAgIFxyXG4gICAgaWYgKHN0b3JlZENvbGxlZ2VJZCkge1xyXG4gICAgICBjb25zb2xlLmxvZyhcIkZvdW5kIGNvbGxlZ2VJZCBpbiBsb2NhbFN0b3JhZ2U6XCIsIHN0b3JlZENvbGxlZ2VJZCk7XHJcbiAgICAgIHNldENvbGxlZ2VJZChzdG9yZWRDb2xsZWdlSWQpO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcbiAgICBcclxuICAgIC8vIElmIG5vdCBmb3VuZCBpbiBsb2NhbFN0b3JhZ2UsIHRyeSB0byBleHRyYWN0IGZyb20gSldUIHRva2VuXHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCBwb3NzaWJsZVRva2VuS2V5cyA9IFsndG9rZW4nLCAnYmFja2VuZFRva2VuJywgJ2F1dGhUb2tlbicsICdqd3RUb2tlbiddO1xyXG4gICAgICBcclxuICAgICAgZm9yIChjb25zdCBrZXkgb2YgcG9zc2libGVUb2tlbktleXMpIHtcclxuICAgICAgICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKGtleSk7XHJcbiAgICAgICAgaWYgKHRva2VuKSB7XHJcbiAgICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICBjb25zdCBwYXJ0cyA9IHRva2VuLnNwbGl0KCcuJyk7XHJcbiAgICAgICAgICAgIGlmIChwYXJ0cy5sZW5ndGggPT09IDMpIHtcclxuICAgICAgICAgICAgICBjb25zdCBwYXlsb2FkID0gSlNPTi5wYXJzZShhdG9iKHBhcnRzWzFdKSk7XHJcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coXCJKV1QgcGF5bG9hZDpcIiwgcGF5bG9hZCk7XHJcbiAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgaWYgKHBheWxvYWQuY29sbGVnZUlkKSB7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhgRm91bmQgY29sbGVnZUlkIGluICR7a2V5fTpgLCBwYXlsb2FkLmNvbGxlZ2VJZCk7XHJcbiAgICAgICAgICAgICAgICBzZXRDb2xsZWdlSWQocGF5bG9hZC5jb2xsZWdlSWQpO1xyXG4gICAgICAgICAgICAgICAgLy8gU3RvcmUgaXQgaW4gbG9jYWxTdG9yYWdlIGZvciBmdXR1cmUgdXNlXHJcbiAgICAgICAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnY29sbGVnZUlkJywgcGF5bG9hZC5jb2xsZWdlSWQpO1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfSBjYXRjaCAoZSkge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKGBFcnJvciBwYXJzaW5nIHRva2VuIGZyb20gJHtrZXl9OmAsIGUpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgICBcclxuICAgICAgLy8gTG9nIGFsbCBsb2NhbFN0b3JhZ2Uga2V5cyBmb3IgZGVidWdnaW5nXHJcbiAgICAgIGNvbnNvbGUubG9nKFwiQWxsIGxvY2FsU3RvcmFnZSBrZXlzOlwiLCBPYmplY3Qua2V5cyhsb2NhbFN0b3JhZ2UpKTtcclxuICAgICAgY29uc29sZS5lcnJvcihcIkNvdWxkIG5vdCBmaW5kIGNvbGxlZ2VJZCBpbiBhbnkgdG9rZW4gb3IgbG9jYWxTdG9yYWdlXCIpO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZ2V0dGluZyBjb2xsZWdlSWQ6JywgZXJyb3IpO1xyXG4gICAgfVxyXG4gIH0sIFtdKTtcclxuXHJcbiAgLy8gRmV0Y2ggdGVhY2hlcnMgZGF0YVxyXG4gIGNvbnN0IHsgXHJcbiAgICBkYXRhOiB0ZWFjaGVyc0RhdGEgPSBbXSwgXHJcbiAgICBpc0xvYWRpbmcsXHJcbiAgICByZWZldGNoLFxyXG4gICAgZXJyb3JcclxuICB9ID0gdXNlUXVlcnkoe1xyXG4gICAgcXVlcnlLZXk6IFsndGVhY2hlcnMnLCBjb2xsZWdlSWQsIGN1cnJlbnRQYWdlLCBwYWdlU2l6ZSwgZmlsdGVyc10sXHJcbiAgICBxdWVyeUZuOiBhc3luYyAoKSA9PiB7XHJcbiAgICAgIGlmICghY29sbGVnZUlkKSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coXCJObyBjb2xsZWdlSWQgZm91bmQsIHJldHVybmluZyBlbXB0eSBhcnJheVwiKTtcclxuICAgICAgICByZXR1cm4gW107XHJcbiAgICAgIH1cclxuICAgICAgXHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coYEZldGNoaW5nIHRlYWNoZXJzIGZvciBjb2xsZWdlSWQ6ICR7Y29sbGVnZUlkfSwgcGFnZTogJHtjdXJyZW50UGFnZX0sIGxpbWl0OiAke3BhZ2VTaXplfWApO1xyXG4gICAgICAgIFxyXG4gICAgICAgIC8vIENyZWF0ZSBmaWx0ZXIgb2JqZWN0IGZvciBBUElcclxuICAgICAgICBjb25zdCBhcGlGaWx0ZXJzOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+ID0ge307XHJcbiAgICAgICAgaWYgKGZpbHRlcnMubmFtZSkgYXBpRmlsdGVycy5uYW1lID0gZmlsdGVycy5uYW1lO1xyXG4gICAgICAgIGlmIChmaWx0ZXJzLmVtYWlsKSBhcGlGaWx0ZXJzLmVtYWlsID0gZmlsdGVycy5lbWFpbDtcclxuICAgICAgICBpZiAoZmlsdGVycy5kZXBhcnRtZW50ICYmIGZpbHRlcnMuZGVwYXJ0bWVudCAhPT0gJ2FsbF9kZXBhcnRtZW50cycpIHtcclxuICAgICAgICAgIGFwaUZpbHRlcnMuZGVwYXJ0bWVudCA9IGZpbHRlcnMuZGVwYXJ0bWVudDtcclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKGZpbHRlcnMuc3RhdHVzICYmIGZpbHRlcnMuc3RhdHVzICE9PSAnYWxsJykge1xyXG4gICAgICAgICAgYXBpRmlsdGVycy5zdGF0dXMgPSBmaWx0ZXJzLnN0YXR1cztcclxuICAgICAgICB9XHJcbiAgICAgICAgXHJcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBnZXRDb2xsZWdlVGVhY2hlcnMoY29sbGVnZUlkLCBjdXJyZW50UGFnZSwgcGFnZVNpemUsIGFwaUZpbHRlcnMpO1xyXG5cclxuICAgICAgICBjb25zb2xlLmxvZyhcIkFQSSByZXR1cm5lZCByZXNwb25zZTpcIiwgcmVzcG9uc2UpO1xyXG5cclxuICAgICAgICAvLyBDaGVjayBpZiB0aGUgQVBJIGNhbGwgd2FzIHN1Y2Nlc3NmdWxcclxuICAgICAgICBpZiAoIWlzQXBpU3VjY2VzcyhyZXNwb25zZSkpIHtcclxuICAgICAgICAgIC8vIEVycm9yIGlzIGFscmVhZHkgaGFuZGxlZCBieSB0aGUgQVBJIGZ1bmN0aW9uICh0b2FzdCBzaG93bilcclxuICAgICAgICAgIHJldHVybiBbXTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGNvbnN0IGRhdGEgPSByZXNwb25zZS5kYXRhO1xyXG5cclxuICAgICAgICAvLyBIYW5kbGUgYm90aCByZXNwb25zZSBmb3JtYXRzIChhcnJheSBvciBvYmplY3Qgd2l0aCB0ZWFjaGVycyBwcm9wZXJ0eSlcclxuICAgICAgICBsZXQgdGVhY2hlcnNBcnJheSA9IFtdO1xyXG4gICAgICAgIGxldCB0b3RhbCA9IDA7XHJcbiAgICAgICAgbGV0IHRvdGFsUGFnZXMgPSAxO1xyXG5cclxuICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShkYXRhKSkge1xyXG4gICAgICAgICAgLy8gQVBJIHJldHVybmVkIGFuIGFycmF5IGRpcmVjdGx5XHJcbiAgICAgICAgICB0ZWFjaGVyc0FycmF5ID0gZGF0YTtcclxuICAgICAgICAgIHRvdGFsID0gZGF0YS5sZW5ndGg7XHJcbiAgICAgICAgICB0b3RhbFBhZ2VzID0gMTtcclxuICAgICAgICB9IGVsc2UgaWYgKGRhdGEgJiYgZGF0YS50ZWFjaGVycykge1xyXG4gICAgICAgICAgLy8gQVBJIHJldHVybmVkIGFuIG9iamVjdCB3aXRoIHRlYWNoZXJzIHByb3BlcnR5XHJcbiAgICAgICAgICB0ZWFjaGVyc0FycmF5ID0gZGF0YS50ZWFjaGVycztcclxuICAgICAgICAgIHRvdGFsID0gZGF0YS50b3RhbCB8fCBkYXRhLnRlYWNoZXJzLmxlbmd0aDtcclxuICAgICAgICAgIHRvdGFsUGFnZXMgPSBkYXRhLnRvdGFsUGFnZXMgfHwgTWF0aC5jZWlsKHRvdGFsIC8gcGFnZVNpemUpO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICBjb25zb2xlLmVycm9yKFwiQVBJIHJldHVybmVkIGludmFsaWQgZGF0YSBmb3JtYXQ6XCIsIGRhdGEpO1xyXG4gICAgICAgICAgcmV0dXJuIFtdO1xyXG4gICAgICAgIH1cclxuICAgICAgICBcclxuICAgICAgICAvLyBTZXQgdG90YWwgY291bnRzIGZvciBwYWdpbmF0aW9uXHJcbiAgICAgICAgc2V0VG90YWxUZWFjaGVycyh0b3RhbCk7XHJcbiAgICAgICAgc2V0VG90YWxQYWdlcyh0b3RhbFBhZ2VzKTtcclxuICAgICAgICBcclxuICAgICAgICAvLyBUcmFuc2Zvcm0gdGhlIGRhdGEgdG8gbWF0Y2ggdGhlIFRlYWNoZXJEYXRhIGludGVyZmFjZVxyXG4gICAgICAgIGNvbnN0IHRyYW5zZm9ybWVkRGF0YSA9IHRlYWNoZXJzQXJyYXkubWFwKCh0ZWFjaGVyOiBhbnkpID0+IHtcclxuICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgIGlkOiB0ZWFjaGVyLl9pZCxcclxuICAgICAgICAgICAgbmFtZTogdGVhY2hlci5kaXNwbGF5TmFtZSB8fCB0ZWFjaGVyLm5hbWUgfHwgJ1Vua25vd24nLFxyXG4gICAgICAgICAgICBlbWFpbDogdGVhY2hlci5lbWFpbCB8fCAnTm8gZW1haWwnLFxyXG4gICAgICAgICAgICBkZXBhcnRtZW50OiB0ZWFjaGVyLmRlcGFydG1lbnQgfHwgJ04vQScsXHJcbiAgICAgICAgICAgIHBob25lOiB0ZWFjaGVyLnBob25lIHx8ICdOL0EnLFxyXG4gICAgICAgICAgICBzdGF0dXM6IHRlYWNoZXIuc3RhdHVzID09PSAnYWN0aXZlJyA/ICdBY3RpdmUnIDogJ0luYWN0aXZlJyxcclxuICAgICAgICAgICAgYXZhdGFyOiB0ZWFjaGVyLmF2YXRhciB8fCBudWxsXHJcbiAgICAgICAgICB9O1xyXG4gICAgICAgIH0pO1xyXG4gICAgICAgIFxyXG4gICAgICAgIHJldHVybiB0cmFuc2Zvcm1lZERhdGE7XHJcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGZldGNoIHRlYWNoZXJzOicsIGVycm9yKTtcclxuICAgICAgICB0b2FzdC5lcnJvcignRmFpbGVkIHRvIGxvYWQgdGVhY2hlcnMuIFBsZWFzZSB0cnkgYWdhaW4uJyk7XHJcbiAgICAgICAgcmV0dXJuIFtdO1xyXG4gICAgICB9XHJcbiAgICB9LFxyXG4gICAgZW5hYmxlZDogISFjb2xsZWdlSWQsXHJcbiAgICBzdGFsZVRpbWU6IDAsXHJcbiAgICByZWZldGNoT25XaW5kb3dGb2N1czogdHJ1ZVxyXG4gIH0pO1xyXG5cclxuICAvLyBBZGQgZGVidWdnaW5nIHRvIHNlZSBpZiB0aGVyZSdzIGFuIGVycm9yXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiUXVlcnkgZXJyb3I6XCIsIGVycm9yKTtcclxuICAgIH1cclxuICB9LCBbZXJyb3JdKTtcclxuXHJcbiAgLy8gRXh0cmFjdCB1bmlxdWUgZGVwYXJ0bWVudHMgZm9yIGZpbHRlciBkcm9wZG93blxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zb2xlLmxvZyhcInRlYWNoZXJzRGF0YSBjaGFuZ2VkOlwiLCB0ZWFjaGVyc0RhdGEpO1xyXG4gICAgY29uc29sZS5sb2coXCJ0ZWFjaGVyc0RhdGEgbGVuZ3RoOlwiLCB0ZWFjaGVyc0RhdGE/Lmxlbmd0aCB8fCAwKTtcclxuICAgIFxyXG4gICAgaWYgKHRlYWNoZXJzRGF0YSAmJiB0ZWFjaGVyc0RhdGEubGVuZ3RoID4gMCkge1xyXG4gICAgICBpbnRlcmZhY2UgRGVwYXJ0bWVudE1hcCB7XHJcbiAgICAgICAgW2RlcGFydG1lbnQ6IHN0cmluZ106IGJvb2xlYW47XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnN0IHVuaXF1ZURlcGFydG1lbnRzOiBzdHJpbmdbXSA9IEFycmF5LmZyb20oXHJcbiAgICAgICAgbmV3IFNldChcclxuICAgICAgICAgICh0ZWFjaGVyc0RhdGEgYXMgVGVhY2hlckRhdGFbXSlcclxuICAgICAgICAubWFwKCh0ZWFjaGVyOiBUZWFjaGVyRGF0YSkgPT4gdGVhY2hlci5kZXBhcnRtZW50KVxyXG4gICAgICAgIC5maWx0ZXIoQm9vbGVhbikgYXMgc3RyaW5nW11cclxuICAgICAgICApXHJcbiAgICAgICk7XHJcbiAgICAgIHNldERlcGFydG1lbnRzKHVuaXF1ZURlcGFydG1lbnRzKTtcclxuICAgICAgXHJcbiAgICAgIC8vIE1ha2Ugc3VyZSB3ZSdyZSBzZXR0aW5nIHRoZSBmaWx0ZXJlZCB0ZWFjaGVycyB0byB0aGUgZnVsbCBkYXRhIHNldCBpbml0aWFsbHlcclxuICAgICAgc2V0RmlsdGVyZWRUZWFjaGVycyhbLi4udGVhY2hlcnNEYXRhXSk7XHJcbiAgICAgIGNvbnNvbGUubG9nKFwiU2V0dGluZyBmaWx0ZXJlZCB0ZWFjaGVycyB0bzpcIiwgdGVhY2hlcnNEYXRhLmxlbmd0aCk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBjb25zb2xlLmxvZyhcIk5vIHRlYWNoZXJzIGRhdGEgdG8gc2V0XCIpO1xyXG4gICAgfVxyXG4gIH0sIFt0ZWFjaGVyc0RhdGFdKTtcclxuXHJcbiAgY29uc3QgaGFuZGxlVGFiQ2hhbmdlID0gKHRhYjogc3RyaW5nKSA9PiB7XHJcbiAgICBjb25zb2xlLmxvZygnUGFnZSByZWNlaXZlZCB0YWIgY2hhbmdlOicsIHRhYik7XHJcbiAgICBcclxuICAgIC8vIElmIHN3aXRjaGluZyB0byBlZGl0IG1vZGUgZnJvbSB2aWV3LCBkb24ndCBjbGVhciB0aGUgZWRpdGluZyB0ZWFjaGVyXHJcbiAgICBpZiAodGFiICE9PSAnZWRpdCcpIHtcclxuICAgICAgc2V0RWRpdGluZ1RlYWNoZXIobnVsbCk7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIC8vIFNldCB0aGUgYWN0aXZlIHRhYiB3aXRoIGEgc21hbGwgZGVsYXkgdG8gYWxsb3cgZm9yIGFuaW1hdGlvblxyXG4gICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgIHNldEFjdGl2ZVRhYih0YWIpO1xyXG4gICAgfSwgMTAwKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVUZWFjaGVyQWRkZWQgPSAoKSA9PiB7XHJcbiAgICByZWZldGNoKCk7IC8vIFJlZnJlc2ggdGhlIHRlYWNoZXJzIGxpc3RcclxuICAgIHNldEFjdGl2ZVRhYigndmlldycpOyAvLyBTd2l0Y2ggYmFjayB0byB2aWV3IHRhYlxyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZUVkaXQgPSAoaWQ6IHN0cmluZykgPT4ge1xyXG4gICAgY29uc29sZS5sb2coYEVkaXQgdGVhY2hlciB3aXRoIGlkOiAke2lkfWApO1xyXG4gICAgY29uc3QgdGVhY2hlciA9IHRlYWNoZXJzRGF0YS5maW5kKCh0OiBUZWFjaGVyRGF0YSkgPT4gdC5pZCA9PT0gaWQpO1xyXG4gICAgaWYgKHRlYWNoZXIpIHtcclxuICAgICAgc2V0RWRpdGluZ1RlYWNoZXIodGVhY2hlcik7XHJcbiAgICAgIHNldEFjdGl2ZVRhYignZWRpdCcpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZURlbGV0ZSA9IChpZDogc3RyaW5nKSA9PiB7XHJcbiAgICBjb25zb2xlLmxvZyhgRGVsZXRlIHRlYWNoZXIgd2l0aCBpZDogJHtpZH1gKTtcclxuICAgIHNldFRlYWNoZXJUb0RlbGV0ZShpZCk7XHJcbiAgICBzZXRJc0RlbGV0ZURpYWxvZ09wZW4odHJ1ZSk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgY29uZmlybURlbGV0ZSA9IGFzeW5jICgpID0+IHtcclxuICAgIGlmICghdGVhY2hlclRvRGVsZXRlKSByZXR1cm47XHJcbiAgICBcclxuICAgIHRyeSB7XHJcbiAgICAgIGF3YWl0IGRlbGV0ZVRlYWNoZXIodGVhY2hlclRvRGVsZXRlKTtcclxuICAgICAgdG9hc3Quc3VjY2VzcygnVGVhY2hlciBkZWxldGVkIHN1Y2Nlc3NmdWxseScpO1xyXG4gICAgICByZWZldGNoKCk7IC8vIFJlZnJlc2ggdGhlIGxpc3RcclxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcclxuICAgICAgdG9hc3QuZXJyb3IoZXJyb3IubWVzc2FnZSB8fCAnRmFpbGVkIHRvIGRlbGV0ZSB0ZWFjaGVyJyk7XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBzZXRJc0RlbGV0ZURpYWxvZ09wZW4oZmFsc2UpO1xyXG4gICAgICBzZXRUZWFjaGVyVG9EZWxldGUobnVsbCk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlVXBkYXRlVGVhY2hlciA9IGFzeW5jIChmb3JtRGF0YTogYW55KSA9PiB7XHJcbiAgICBpZiAoIWVkaXRpbmdUZWFjaGVyKSByZXR1cm47XHJcbiAgICBcclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIE9ubHkgc2VuZCB0aGUgYWxsb3dlZCBmaWVsZHMgYW5kIGVuc3VyZSBzdGF0dXMgaXMgbG93ZXJjYXNlXHJcbiAgICAgIGNvbnN0IHVwZGF0ZURhdGEgPSB7XHJcbiAgICAgICAgcGhvbmU6IGZvcm1EYXRhLnBob25lLFxyXG4gICAgICAgIGRlcGFydG1lbnQ6IGZvcm1EYXRhLmRlcGFydG1lbnQsXHJcbiAgICAgICAgZGVzaWduYXRpb246IGZvcm1EYXRhLmRlc2lnbmF0aW9uLFxyXG4gICAgICAgIHN0YXR1czogZm9ybURhdGEuc3RhdHVzLnRvTG93ZXJDYXNlKClcclxuICAgICAgfTtcclxuICAgICAgXHJcbiAgICAgIGF3YWl0IHVwZGF0ZVRlYWNoZXIoZWRpdGluZ1RlYWNoZXIuaWQsIHVwZGF0ZURhdGEpO1xyXG4gICAgICB0b2FzdC5zdWNjZXNzKCdUZWFjaGVyIHVwZGF0ZWQgc3VjY2Vzc2Z1bGx5Jyk7XHJcbiAgICAgIHJlZmV0Y2goKTsgLy8gUmVmcmVzaCB0aGUgbGlzdFxyXG4gICAgICBzZXRBY3RpdmVUYWIoJ3ZpZXcnKTsgLy8gU3dpdGNoIGJhY2sgdG8gdmlldyB0YWJcclxuICAgICAgc2V0RWRpdGluZ1RlYWNoZXIobnVsbCk7XHJcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byB1cGRhdGUgdGVhY2hlcjonLCBlcnJvcik7XHJcbiAgICAgIHRvYXN0LmVycm9yKGVycm9yLm1lc3NhZ2UgfHwgJ0ZhaWxlZCB0byB1cGRhdGUgdGVhY2hlcicpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZU9wZW5GaWx0ZXJNb2RhbCA9ICgpID0+IHtcclxuICAgIHNldElzRmlsdGVyTW9kYWxPcGVuKHRydWUpO1xyXG4gIH07XHJcbmNvbnN0IGhhbmRsZUZpbHRlckFwcGx5ID0gYXN5bmMgKGZpbHRlclZhbHVlczogVGVhY2hlckZpbHRlclZhbHVlcykgPT4ge1xyXG4gIC8vIFVwZGF0ZSBmaWx0ZXIgc3RhdGUgdXNlZCBieSBSZWFjdCBRdWVyeVxyXG4gIHNldEZpbHRlcnMoZmlsdGVyVmFsdWVzKTtcclxuICAvLyBSZXNldCB0byBwYWdlIDEgd2hlbmV2ZXIgbmV3IGZpbHRlciBpcyBhcHBsaWVkXHJcbiAgc2V0Q3VycmVudFBhZ2UoMSk7XHJcblxyXG4gIC8vIFJlZmV0Y2ggdGVhY2hlciBsaXN0IHVzaW5nIHVwZGF0ZWQgZmlsdGVyc1xyXG4gIGlmIChjb2xsZWdlSWQpIHtcclxuICAgIHJlZmV0Y2goKTtcclxuICB9XHJcblxyXG4gIC8vIE9wdGlvbmFsOiBJbmZvcm0gdXNlciB3aGVuIGRlZmF1bHQgZmlsdGVyIChpLmUuLCBhbGwgZmllbGRzIGVtcHR5KSBpcyBhcHBsaWVkXHJcbiAgY29uc3QgaXNFbXB0eUZpbHRlciA9XHJcbiAgICAhZmlsdGVyVmFsdWVzLm5hbWUgJiZcclxuICAgICFmaWx0ZXJWYWx1ZXMuZW1haWwgJiZcclxuICAgICghZmlsdGVyVmFsdWVzLmRlcGFydG1lbnQgfHwgZmlsdGVyVmFsdWVzLmRlcGFydG1lbnQgPT09ICdhbGxfZGVwYXJ0bWVudHMnKSAmJlxyXG4gICAgKCFmaWx0ZXJWYWx1ZXMuc3RhdHVzIHx8IGZpbHRlclZhbHVlcy5zdGF0dXMgPT09ICdhbGwnKTtcclxuXHJcbiAgaWYgKGlzRW1wdHlGaWx0ZXIpIHtcclxuICAgIHRvYXN0LmluZm8oJ1Nob3dpbmcgYWxsIHRlYWNoZXJzJyk7XHJcbiAgfVxyXG59O1xyXG5cclxuXHJcbiAgY29uc3QgaGFuZGxlUGFnZUNoYW5nZSA9IChwYWdlOiBudW1iZXIpID0+IHtcclxuICAgIHNldEN1cnJlbnRQYWdlKHBhZ2UpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZVBhZ2VTaXplQ2hhbmdlID0gKHNpemU6IG51bWJlcikgPT4ge1xyXG4gICAgc2V0UGFnZVNpemUoc2l6ZSk7XHJcbiAgICBzZXRDdXJyZW50UGFnZSgxKTsgLy8gUmVzZXQgdG8gZmlyc3QgcGFnZSB3aGVuIGNoYW5naW5nIHBhZ2Ugc2l6ZVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IHBhZ2luYXRlZFRlYWNoZXJzID0gKGZpbHRlcmVkVGVhY2hlcnMubGVuZ3RoID4gMCA/IGZpbHRlcmVkVGVhY2hlcnMgOiB0ZWFjaGVyc0RhdGEpLnNsaWNlKFxyXG4gIChjdXJyZW50UGFnZSAtIDEpICogcGFnZVNpemUsXHJcbiAgY3VycmVudFBhZ2UgKiBwYWdlU2l6ZVxyXG4pO1xyXG5cclxuICBjb25zdCByZW5kZXJUZWFjaGVyc1RhYmxlID0gKCkgPT4ge1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sXCI+XHJcbiAgICAgICAgPFRlYWNoZXJzVGFibGUgXHJcbiAgICAgICAgICBkYXRhPXtwYWdpbmF0ZWRUZWFjaGVyc30gXHJcbiAgICAgICAgICBvbkVkaXQ9e2hhbmRsZUVkaXR9IFxyXG4gICAgICAgICAgb25EZWxldGU9e2hhbmRsZURlbGV0ZX0gXHJcbiAgICAgICAgICBpdGVtc1BlclBhZ2U9e3BhZ2VTaXplfVxyXG4gICAgICAgICAgaXNMb2FkaW5nPXtpc0xvYWRpbmd9XHJcbiAgICAgICAgICBvblJlZnJlc2g9eygpID0+IHtcclxuICAgICAgICAgICAgLy8gUmVzZXQgZmlsdGVycyBhbmQgc2hvdyBhbGwgdGVhY2hlcnNcclxuICAgICAgICAgICAgc2V0RmlsdGVycyh7XHJcbiAgICAgICAgICAgICAgbmFtZTogJycsXHJcbiAgICAgICAgICAgICAgZGVwYXJ0bWVudDogJycsXHJcbiAgICAgICAgICAgICAgc3RhdHVzOiAnYWxsJyxcclxuICAgICAgICAgICAgICBlbWFpbDogJydcclxuICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgIHNldEZpbHRlcmVkVGVhY2hlcnMoW10pO1xyXG4gICAgICAgICAgICByZWZldGNoKCk7XHJcbiAgICAgICAgICB9fVxyXG4gICAgICAgICAgb25GaWx0ZXI9e2hhbmRsZU9wZW5GaWx0ZXJNb2RhbH1cclxuICAgICAgICAgIGNvbHVtbnM9e1tcIm5hbWVcIiwgXCJkZXBhcnRtZW50XCIsIFwiZW1haWxcIiwgXCJwaG9uZVwiLCBcInN0YXR1c1wiLCBcImFjdGlvbnNcIl19XHJcbiAgICAgICAgLz5cclxuICAgICAgICBcclxuICAgICAgICB7IWlzTG9hZGluZyAmJiB0ZWFjaGVyc0RhdGEubGVuZ3RoID4gMCAmJiAoXHJcbiAgICAgICAgICA8UGFnaW5hdGlvblxyXG4gIGN1cnJlbnRQYWdlPXtjdXJyZW50UGFnZX1cclxuICB0b3RhbFBhZ2VzPXtNYXRoLmNlaWwoXHJcbiAgICAoZmlsdGVyZWRUZWFjaGVycy5sZW5ndGggPiAwID8gZmlsdGVyZWRUZWFjaGVycy5sZW5ndGggOiB0b3RhbFRlYWNoZXJzKSAvIHBhZ2VTaXplXHJcbiAgKX1cclxuICBwYWdlU2l6ZT17cGFnZVNpemV9XHJcbiAgdG90YWxJdGVtcz17ZmlsdGVyZWRUZWFjaGVycy5sZW5ndGggPiAwID8gZmlsdGVyZWRUZWFjaGVycy5sZW5ndGggOiB0b3RhbFRlYWNoZXJzfVxyXG4gIG9uUGFnZUNoYW5nZT17aGFuZGxlUGFnZUNoYW5nZX1cclxuICBvblBhZ2VTaXplQ2hhbmdlPXtoYW5kbGVQYWdlU2l6ZUNoYW5nZX1cclxuICBwYWdlU2l6ZU9wdGlvbnM9e1s1LCAxMCwgMjAsIDUwXX1cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgKX1cclxuICAgICAgPC9kaXY+XHJcbiAgICApO1xyXG4gIH07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBweS02XCI+XHJcbiAgICAgIHshY29sbGVnZUlkICYmIChcclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTYgcC00IGJvcmRlciBib3JkZXIteWVsbG93LTMwMCBiZy15ZWxsb3ctNTAgcm91bmRlZC1tZFwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHNwYWNlLXktMlwiPlxyXG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXllbGxvdy04MDBcIj5cclxuICAgICAgICAgICAgICBDb2xsZWdlIElEIG5vdCBmb3VuZC4gUGxlYXNlIGVudGVyIGl0IG1hbnVhbGx5IG9yIGNoZWNrIHlvdXIgbG9naW4gc3RhdHVzLlxyXG4gICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgIHshc2hvd0NvbGxlZ2VJZElucHV0ID8gKFxyXG4gICAgICAgICAgICAgIDxCdXR0b24gXHJcbiAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiIFxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0NvbGxlZ2VJZElucHV0KHRydWUpfVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIEVudGVyIENvbGxlZ2UgSUQgTWFudWFsbHlcclxuICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0yXCI+XHJcbiAgICAgICAgICAgICAgICA8aW5wdXRcclxuICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxyXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17bWFudWFsQ29sbGVnZUlkfVxyXG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldE1hbnVhbENvbGxlZ2VJZChlLnRhcmdldC52YWx1ZSl9XHJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgQ29sbGVnZSBJRFwiXHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTMgcHktMiBib3JkZXIgcm91bmRlZC1tZCBmbGV4LTFcIlxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgIDxCdXR0b24gb25DbGljaz17aGFuZGxlTWFudWFsQ29sbGVnZUlkU3VibWl0fT5cclxuICAgICAgICAgICAgICAgICAgU3VibWl0XHJcbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgIDxCdXR0b24gXHJcbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCIgXHJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dDb2xsZWdlSWRJbnB1dChmYWxzZSl9XHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIENhbmNlbFxyXG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgKX1cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cclxuICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0cmFja2luZy10aWdodFwiPlxyXG4gICAgICAgICAgICB7YWN0aXZlVGFiID09PSAndmlldycgPyBcIlRlYWNoZXIncyBMaXN0IFZpZXdcIiA6IFxyXG4gICAgICAgICAgICAgYWN0aXZlVGFiID09PSAnYWRkJyA/IFwiQWRkIFRlYWNoZXJzXCIgOiBcclxuICAgICAgICAgICAgIGFjdGl2ZVRhYiA9PT0gJ2VkaXQnID8gXCJFZGl0IFRlYWNoZXJcIiA6IFxyXG4gICAgICAgICAgICAgXCJUZWFjaGVyIEFjdGl2aXR5IExvZ3NcIn1cclxuICAgICAgICAgIDwvaDE+XHJcbiAgICAgICAgICA8QnJlYWRjcnVtYlxyXG4gICAgICAgICAgICBpdGVtcz17W1xyXG4gICAgICAgICAgICAgIHsgbGFiZWw6ICdIb21lJywgaHJlZjogJy8nIH0sXHJcbiAgICAgICAgICAgICAgeyBsYWJlbDogJy4uLicsIGhyZWY6ICcjJyB9LFxyXG4gICAgICAgICAgICAgIHsgbGFiZWw6IGFjdGl2ZVRhYiA9PT0gJ3ZpZXcnID8gJ1RlYWNoZXIgbGlzdCcgOiBcclxuICAgICAgICAgICAgICAgICAgICAgIGFjdGl2ZVRhYiA9PT0gJ2FkZCcgPyAnQWRkIHRlYWNoZXJzJyA6IFxyXG4gICAgICAgICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSAnZWRpdCcgPyAnRWRpdCB0ZWFjaGVyJyA6IFxyXG4gICAgICAgICAgICAgICAgICAgICAgJ1RlYWNoZXIgYWN0aXZpdHkgbG9ncycgfVxyXG4gICAgICAgICAgICBdfVxyXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJtdC0yXCJcclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicm91bmRlZC1sZyBib3JkZXIgYmctY2FyZCB0ZXh0LWNhcmQtZm9yZWdyb3VuZCBzaGFkb3dcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIHB4LTYgcHQtNlwiPlxyXG4gICAgICAgICAgICA8VGVhY2hlclRhYnNcclxuICAgICAgICAgICAgICBhY3RpdmVUYWI9e2FjdGl2ZVRhYiA9PT0gJ2VkaXQnID8gJ3ZpZXcnIDogYWN0aXZlVGFifVxyXG4gICAgICAgICAgICAgIG9uVGFiQ2hhbmdlPXtoYW5kbGVUYWJDaGFuZ2V9XHJcbiAgICAgICAgICAgICAgY29sbGVnZUlkTWlzc2luZz17IWNvbGxlZ2VJZH1cclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgXHJcbiAgICAgICAgICAgIHsvKiB7YWN0aXZlVGFiID09PSAndmlldycgJiYgKFxyXG4gICAgICAgICAgICAgIDxCdXR0b24gXHJcbiAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiIFxyXG4gICAgICAgICAgICAgICAgc2l6ZT1cInNtXCIgXHJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKFwiTWFudWFsIHJlZnJlc2ggdHJpZ2dlcmVkXCIpO1xyXG4gICAgICAgICAgICAgICAgICByZWZldGNoKCk7XHJcbiAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTFcIlxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxSZWZyZXNoQ3cgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XHJcbiAgICAgICAgICAgICAgICA8c3Bhbj5SZWZyZXNoPC9zcGFuPlxyXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICApfSAqL31cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgXHJcbiAgICAgICAgICB7YWN0aXZlVGFiID09PSAndmlldycgJiYgcmVuZGVyVGVhY2hlcnNUYWJsZSgpfVxyXG5cclxuICAgICAgICAgIHthY3RpdmVUYWIgPT09ICdhZGQnICYmIChcclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTZcIj5cclxuICAgICAgICAgICAgICA8QWRkVGVhY2hlckZvcm0gXHJcbiAgICAgICAgICAgICAgICBvbkNhbmNlbD17KCkgPT4gaGFuZGxlVGFiQ2hhbmdlKCd2aWV3Jyl9IFxyXG4gICAgICAgICAgICAgICAgb25TdWNjZXNzPXtoYW5kbGVUZWFjaGVyQWRkZWR9XHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgIHthY3RpdmVUYWIgPT09ICdlZGl0JyAmJiBlZGl0aW5nVGVhY2hlciAmJiAoXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC02XCI+XHJcbiAgICAgICAgICAgICAgPEVkaXRUZWFjaGVyRm9ybSBcclxuICAgICAgICAgICAgICAgIHRlYWNoZXI9e2VkaXRpbmdUZWFjaGVyfVxyXG4gICAgICAgICAgICAgICAgb25DYW5jZWw9eygpID0+IGhhbmRsZVRhYkNoYW5nZSgndmlldycpfVxyXG4gICAgICAgICAgICAgICAgb25TdWJtaXQ9e2hhbmRsZVVwZGF0ZVRlYWNoZXJ9XHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgIHsvKiB7YWN0aXZlVGFiID09PSAnbG9ncycgJiYgKFxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNlwiPlxyXG4gICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCBtYi04IG1kOm1iLTBcIj5UZWFjaGVyIEFjdGl2aXR5IExvZ3M8L2gyPlxyXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxyXG4gICAgICAgICAgICAgICAgVGVhY2hlciBhY3Rpdml0eSBsb2dzIHdvdWxkIGJlIGRpc3BsYXllZCBoZXJlLiBUaGlzIGlzIGEgcGxhY2Vob2xkZXIgZm9yIHRoZSBUZWFjaGVyIEFjdGl2aXR5IExvZ3MgdGFiLlxyXG4gICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICApfSAqL31cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICA8RGlhbG9nIG9wZW49e2lzRGVsZXRlRGlhbG9nT3Blbn0gb25PcGVuQ2hhbmdlPXtzZXRJc0RlbGV0ZURpYWxvZ09wZW59PlxyXG4gICAgICAgIDxEaWFsb2dDb250ZW50PlxyXG4gICAgICAgICAgPERpYWxvZ0hlYWRlcj5cclxuICAgICAgICAgICAgPERpYWxvZ1RpdGxlPkNvbmZpcm0gRGVsZXRpb248L0RpYWxvZ1RpdGxlPlxyXG4gICAgICAgICAgICA8RGlhbG9nRGVzY3JpcHRpb24+XHJcbiAgICAgICAgICAgICAgQXJlIHlvdSBzdXJlIHlvdSB3YW50IHRvIGRlbGV0ZSB0aGlzIHRlYWNoZXI/IFRoaXMgYWN0aW9uIGNhbm5vdCBiZSB1bmRvbmUuXHJcbiAgICAgICAgICAgIDwvRGlhbG9nRGVzY3JpcHRpb24+XHJcbiAgICAgICAgICA8L0RpYWxvZ0hlYWRlcj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWVuZCBzcGFjZS14LTQgbXQtNFwiPlxyXG4gICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgb25DbGljaz17KCkgPT4gc2V0SXNEZWxldGVEaWFsb2dPcGVuKGZhbHNlKX0+XHJcbiAgICAgICAgICAgICAgQ2FuY2VsXHJcbiAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJkZXN0cnVjdGl2ZVwiIG9uQ2xpY2s9e2NvbmZpcm1EZWxldGV9PlxyXG4gICAgICAgICAgICAgIERlbGV0ZVxyXG4gICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvRGlhbG9nQ29udGVudD5cclxuICAgICAgPC9EaWFsb2c+XHJcblxyXG4gICAgICB7LyogPEZpbHRlck1vZGFsXHJcbiAgICAgICAgb3Blbj17aXNGaWx0ZXJNb2RhbE9wZW59XHJcbiAgICAgICAgb25PcGVuQ2hhbmdlPXtzZXRJc0ZpbHRlck1vZGFsT3Blbn1cclxuICAgICAgICBvbkZpbHRlcj17aGFuZGxlRmlsdGVyQXBwbHl9XHJcbiAgICAgICAgY3VycmVudEZpbHRlcnM9e2ZpbHRlcnN9XHJcbiAgICAgICAgZGVwYXJ0bWVudHM9e2RlcGFydG1lbnRzfVxyXG4gICAgICAvPiAqL31cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBUZWFjaGVyc0xpc3RQYWdlO1xyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVF1ZXJ5IiwiQnJlYWRjcnVtYiIsInRvYXN0IiwiVGVhY2hlcnNUYWJsZSIsIlRlYWNoZXJUYWJzIiwiQWRkVGVhY2hlckZvcm0iLCJFZGl0VGVhY2hlckZvcm0iLCJnZXRDb2xsZWdlVGVhY2hlcnMiLCJkZWxldGVUZWFjaGVyIiwidXBkYXRlVGVhY2hlciIsImlzQXBpU3VjY2VzcyIsIkRpYWxvZyIsIkRpYWxvZ0NvbnRlbnQiLCJEaWFsb2dIZWFkZXIiLCJEaWFsb2dUaXRsZSIsIkRpYWxvZ0Rlc2NyaXB0aW9uIiwiQnV0dG9uIiwiUGFnaW5hdGlvbiIsIlRlYWNoZXJzTGlzdFBhZ2UiLCJhY3RpdmVUYWIiLCJzZXRBY3RpdmVUYWIiLCJlZGl0aW5nVGVhY2hlciIsInNldEVkaXRpbmdUZWFjaGVyIiwiaXNEZWxldGVEaWFsb2dPcGVuIiwic2V0SXNEZWxldGVEaWFsb2dPcGVuIiwidGVhY2hlclRvRGVsZXRlIiwic2V0VGVhY2hlclRvRGVsZXRlIiwiaXNGaWx0ZXJNb2RhbE9wZW4iLCJzZXRJc0ZpbHRlck1vZGFsT3BlbiIsImZpbHRlcnMiLCJzZXRGaWx0ZXJzIiwibmFtZSIsImRlcGFydG1lbnQiLCJzdGF0dXMiLCJlbWFpbCIsImRlcGFydG1lbnRzIiwic2V0RGVwYXJ0bWVudHMiLCJmaWx0ZXJlZFRlYWNoZXJzIiwic2V0RmlsdGVyZWRUZWFjaGVycyIsImNvbGxlZ2VJZCIsInNldENvbGxlZ2VJZCIsInNob3dDb2xsZWdlSWRJbnB1dCIsInNldFNob3dDb2xsZWdlSWRJbnB1dCIsIm1hbnVhbENvbGxlZ2VJZCIsInNldE1hbnVhbENvbGxlZ2VJZCIsImN1cnJlbnRQYWdlIiwic2V0Q3VycmVudFBhZ2UiLCJwYWdlU2l6ZSIsInNldFBhZ2VTaXplIiwidG90YWxUZWFjaGVycyIsInNldFRvdGFsVGVhY2hlcnMiLCJ0b3RhbFBhZ2VzIiwic2V0VG90YWxQYWdlcyIsImhhbmRsZU1hbnVhbENvbGxlZ2VJZFN1Ym1pdCIsInRyaW0iLCJjb25zb2xlIiwibG9nIiwibG9jYWxTdG9yYWdlIiwic2V0SXRlbSIsInJlZmV0Y2giLCJzdG9yZWRDb2xsZWdlSWQiLCJnZXRJdGVtIiwicG9zc2libGVUb2tlbktleXMiLCJrZXkiLCJ0b2tlbiIsInBhcnRzIiwic3BsaXQiLCJsZW5ndGgiLCJwYXlsb2FkIiwiSlNPTiIsInBhcnNlIiwiYXRvYiIsImUiLCJlcnJvciIsIk9iamVjdCIsImtleXMiLCJkYXRhIiwidGVhY2hlcnNEYXRhIiwiaXNMb2FkaW5nIiwicXVlcnlLZXkiLCJxdWVyeUZuIiwiYXBpRmlsdGVycyIsInJlc3BvbnNlIiwidGVhY2hlcnNBcnJheSIsInRvdGFsIiwiQXJyYXkiLCJpc0FycmF5IiwidGVhY2hlcnMiLCJNYXRoIiwiY2VpbCIsInRyYW5zZm9ybWVkRGF0YSIsIm1hcCIsInRlYWNoZXIiLCJpZCIsIl9pZCIsImRpc3BsYXlOYW1lIiwicGhvbmUiLCJhdmF0YXIiLCJlbmFibGVkIiwic3RhbGVUaW1lIiwicmVmZXRjaE9uV2luZG93Rm9jdXMiLCJ1bmlxdWVEZXBhcnRtZW50cyIsImZyb20iLCJTZXQiLCJmaWx0ZXIiLCJCb29sZWFuIiwiaGFuZGxlVGFiQ2hhbmdlIiwidGFiIiwic2V0VGltZW91dCIsImhhbmRsZVRlYWNoZXJBZGRlZCIsImhhbmRsZUVkaXQiLCJmaW5kIiwidCIsImhhbmRsZURlbGV0ZSIsImNvbmZpcm1EZWxldGUiLCJzdWNjZXNzIiwibWVzc2FnZSIsImhhbmRsZVVwZGF0ZVRlYWNoZXIiLCJmb3JtRGF0YSIsInVwZGF0ZURhdGEiLCJkZXNpZ25hdGlvbiIsInRvTG93ZXJDYXNlIiwiaGFuZGxlT3BlbkZpbHRlck1vZGFsIiwiaGFuZGxlRmlsdGVyQXBwbHkiLCJmaWx0ZXJWYWx1ZXMiLCJpc0VtcHR5RmlsdGVyIiwiaW5mbyIsImhhbmRsZVBhZ2VDaGFuZ2UiLCJwYWdlIiwiaGFuZGxlUGFnZVNpemVDaGFuZ2UiLCJzaXplIiwicGFnaW5hdGVkVGVhY2hlcnMiLCJzbGljZSIsInJlbmRlclRlYWNoZXJzVGFibGUiLCJkaXYiLCJjbGFzc05hbWUiLCJvbkVkaXQiLCJvbkRlbGV0ZSIsIml0ZW1zUGVyUGFnZSIsIm9uUmVmcmVzaCIsIm9uRmlsdGVyIiwiY29sdW1ucyIsInRvdGFsSXRlbXMiLCJvblBhZ2VDaGFuZ2UiLCJvblBhZ2VTaXplQ2hhbmdlIiwicGFnZVNpemVPcHRpb25zIiwicCIsInZhcmlhbnQiLCJvbkNsaWNrIiwiaW5wdXQiLCJ0eXBlIiwidmFsdWUiLCJvbkNoYW5nZSIsInRhcmdldCIsInBsYWNlaG9sZGVyIiwiaDEiLCJpdGVtcyIsImxhYmVsIiwiaHJlZiIsIm9uVGFiQ2hhbmdlIiwiY29sbGVnZUlkTWlzc2luZyIsIm9uQ2FuY2VsIiwib25TdWNjZXNzIiwib25TdWJtaXQiLCJvcGVuIiwib25PcGVuQ2hhbmdlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/college/teachers-list/page.tsx\n"));

/***/ })

});