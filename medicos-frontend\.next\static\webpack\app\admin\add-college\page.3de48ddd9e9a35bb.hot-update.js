"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/add-college/page",{

/***/ "(app-pages-browser)/./src/components/admin/college-registration-form.tsx":
/*!************************************************************!*\
  !*** ./src/components/admin/college-registration-form.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CollegeRegistrationForm: () => (/* binding */ CollegeRegistrationForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _file_uploader__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./file-uploader */ \"(app-pages-browser)/./src/components/admin/file-uploader.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./src/components/ui/form.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _phone_input__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./phone-input */ \"(app-pages-browser)/./src/components/admin/phone-input.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_api_college__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/api/college */ \"(app-pages-browser)/./src/lib/api/college.ts\");\n/* harmony import */ var _lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/utils/errorHandler */ \"(app-pages-browser)/./src/lib/utils/errorHandler.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ CollegeRegistrationForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst MAX_FILE_SIZE = 50 * 1024 * 1024 // 50MB\n;\nconst ACCEPTED_FILE_TYPES = [\n    \"image/jpeg\",\n    \"image/jpg\",\n    \"image/png\",\n    \"image/svg+xml\"\n];\nconst formSchema = zod__WEBPACK_IMPORTED_MODULE_14__.object({\n    collegeName: zod__WEBPACK_IMPORTED_MODULE_14__.string().min(2, {\n        message: \"College name must be at least 2 characters.\"\n    }),\n    phone: zod__WEBPACK_IMPORTED_MODULE_14__.string().min(10, {\n        message: \"Phone number must be valid.\"\n    }),\n    email: zod__WEBPACK_IMPORTED_MODULE_14__.string().email({\n        message: \"Please enter a valid email address.\"\n    }),\n    address: zod__WEBPACK_IMPORTED_MODULE_14__.string().max(100, {\n        message: \"Address must not exceed 100 characters.\"\n    }),\n    logo: zod__WEBPACK_IMPORTED_MODULE_14__.any().optional().refine((files)=>{\n        var _files_;\n        return !files || files.length === 0 || ((_files_ = files[0]) === null || _files_ === void 0 ? void 0 : _files_.size) <= MAX_FILE_SIZE;\n    }, \"Max file size is 50MB.\").refine((files)=>{\n        var _files_;\n        return !files || files.length === 0 || ACCEPTED_FILE_TYPES.includes((_files_ = files[0]) === null || _files_ === void 0 ? void 0 : _files_.type);\n    }, \"Only .jpg, .jpeg, .png and .svg formats are supported.\")\n});\nfunction CollegeRegistrationForm() {\n    _s();\n    const [files, setFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_15__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(formSchema),\n        defaultValues: {\n            collegeName: \"\",\n            phone: \"\",\n            email: \"\",\n            address: \"\"\n        }\n    });\n    async function onSubmit(values) {\n        setIsSubmitting(true);\n        try {\n            // First upload the logo file to get a URL\n            let logoUrl = \"\";\n            // if (files.length > 0) {\n            //   logoUrl = await uploadFile(files[0])\n            // }\n            if (files.length > 0) {\n                logoUrl = await (0,_lib_utils__WEBPACK_IMPORTED_MODULE_13__.fileToBase64)(files[0]); // base64 string\n            }\n            // Prepare data for API\n            const collegeData = {\n                name: values.collegeName,\n                address: values.address,\n                contactPhone: values.phone,\n                contactEmail: values.email,\n                logoUrl: logoUrl\n            };\n            // Create college\n            const response = await (0,_lib_api_college__WEBPACK_IMPORTED_MODULE_11__.createCollege)(collegeData);\n            if ((0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_12__.isApiSuccess)(response)) {\n                // Success toast is already shown by the API function\n                // Reset form\n                handleReset();\n                // Redirect to college list\n                router.push(\"/admin/college\");\n            }\n        // Error case is already handled by the API function (toast shown)\n        } catch (error) {\n            // Fallback error handling for unexpected errors\n            console.error(\"Unexpected error adding college:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_9__.toast)({\n                title: \"Error\",\n                description: \"An unexpected error occurred. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    }\n    function handleReset() {\n        form.reset();\n        setFiles([]);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_5__.Form, {\n        ...form,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: form.handleSubmit(onSubmit),\n            className: \"space-y-8 mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_5__.FormField, {\n                            control: form.control,\n                            name: \"collegeName\",\n                            render: (param)=>{\n                                let { field } = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_5__.FormItem, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_5__.FormLabel, {\n                                            children: \"College name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_5__.FormControl, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                placeholder: \"\",\n                                                ...field\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_5__.FormMessage, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, void 0);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_5__.FormField, {\n                            control: form.control,\n                            name: \"phone\",\n                            render: (param)=>{\n                                let { field } = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_5__.FormItem, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_5__.FormLabel, {\n                                            children: [\n                                                \"Phone\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-muted-foreground font-normal ml-2\",\n                                                    children: \"Required\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 19\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_5__.FormControl, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phone_input__WEBPACK_IMPORTED_MODULE_8__.PhoneInput, {\n                                                ...field\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_5__.FormMessage, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, void 0);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_5__.FormField, {\n                            control: form.control,\n                            name: \"email\",\n                            render: (param)=>{\n                                let { field } = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_5__.FormItem, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_5__.FormLabel, {\n                                            children: \"Email address\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_5__.FormControl, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                placeholder: \"\",\n                                                ...field\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_5__.FormDescription, {\n                                            children: \"We'll never share your details.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_5__.FormMessage, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, void 0);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_5__.FormField, {\n                            control: form.control,\n                            name: \"address\",\n                            render: (param)=>{\n                                let { field } = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_5__.FormItem, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_5__.FormLabel, {\n                                                    children: \"Address details\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 19\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: [\n                                                        field.value.length,\n                                                        \"/100\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 19\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_5__.FormControl, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                placeholder: \"\",\n                                                className: \"resize-none\",\n                                                maxLength: 100,\n                                                ...field\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_5__.FormMessage, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, void 0);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_5__.FormField, {\n                    control: form.control,\n                    name: \"logo\",\n                    render: (param)=>{\n                        let { field } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_5__.FormItem, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_5__.FormLabel, {\n                                    children: [\n                                        \"Upload logo\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-muted-foreground font-normal ml-2\",\n                                            children: \"Optional\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 15\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_5__.FormControl, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_file_uploader__WEBPACK_IMPORTED_MODULE_3__.FileUploader, {\n                                        value: field.value,\n                                        onChange: (files)=>{\n                                            field.onChange(files);\n                                            setFiles(Array.from(files || []));\n                                        },\n                                        maxSize: MAX_FILE_SIZE,\n                                        acceptedTypes: ACCEPTED_FILE_TYPES\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 17\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_5__.FormMessage, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 13\n                        }, void 0);\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            type: \"submit\",\n                            className: \"bg-blue-500 hover:bg-blue-600\",\n                            disabled: isSubmitting,\n                            children: isSubmitting ? \"Adding...\" : \"Add college\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            type: \"button\",\n                            variant: \"destructive\",\n                            onClick: ()=>form.reset(),\n                            disabled: isSubmitting,\n                            children: \"Cancel\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            type: \"button\",\n                            variant: \"outline\",\n                            onClick: handleReset,\n                            disabled: isSubmitting,\n                            children: \"Reset\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n            lineNumber: 119,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\college-registration-form.tsx\",\n        lineNumber: 118,\n        columnNumber: 5\n    }, this);\n}\n_s(CollegeRegistrationForm, \"RBimYi5Ox+5ZGK3THsteO6jE/TY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_15__.useForm\n    ];\n});\n_c = CollegeRegistrationForm;\nvar _c;\n$RefreshReg$(_c, \"CollegeRegistrationForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/college-registration-form.tsx\n"));

/***/ })

});