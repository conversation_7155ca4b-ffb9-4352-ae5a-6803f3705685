"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/lib/api/analytics.ts":
/*!**********************************!*\
  !*** ./src/lib/api/analytics.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getPlatformSummary: () => (/* binding */ getPlatformSummary),\n/* harmony export */   getQuestionStats: () => (/* binding */ getQuestionStats),\n/* harmony export */   getQuestionUsage: () => (/* binding */ getQuestionUsage),\n/* harmony export */   getTopColleges: () => (/* binding */ getTopColleges)\n/* harmony export */ });\n/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../api */ \"(app-pages-browser)/./src/lib/api.ts\");\n\n/**\n * Get platform summary statistics\n * @returns Platform summary data\n */ const getPlatformSummary = async ()=>{\n    try {\n        return await (0,_api__WEBPACK_IMPORTED_MODULE_0__.apiCall)('/analytics/platform-summary');\n    } catch (error) {\n        console.error('Error fetching platform summary:', error);\n        throw new Error(error.message || 'Failed to fetch platform summary');\n    }\n};\n/**\n * Get top colleges by various metrics\n * @returns Top colleges data\n */ const getTopColleges = async ()=>{\n    try {\n        return await (0,_api__WEBPACK_IMPORTED_MODULE_0__.apiCall)('/analytics/top-colleges');\n    } catch (error) {\n        console.error('Error fetching top colleges:', error);\n        throw new Error(error.message || 'Failed to fetch top colleges data');\n    }\n};\n/**\n * Get question usage statistics\n * @returns Question usage data\n */ const getQuestionUsage = async ()=>{\n    try {\n        return await (0,_api__WEBPACK_IMPORTED_MODULE_0__.apiCall)('/analytics/question-usage');\n    } catch (error) {\n        console.error('Error fetching question usage:', error);\n        throw new Error(error.message || 'Failed to fetch question usage data');\n    }\n};\n/**\n * Get question statistics\n * @returns Question statistics data\n */ const getQuestionStats = async ()=>{\n    try {\n        return await (0,_api__WEBPACK_IMPORTED_MODULE_0__.apiCall)('/analytics/question-stats');\n    } catch (error) {\n        console.error('Error fetching question stats:', error);\n        throw new Error(error.message || 'Failed to fetch question statistics');\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/analytics.ts\n"));

/***/ })

});