"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var QuestionPapersService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuestionPapersService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const question_paper_schema_1 = require("../schema/question-paper.schema");
const question_schema_1 = require("../schema/question.schema");
const subject_schema_1 = require("../schema/subject.schema");
const college_schema_1 = require("../schema/college.schema");
const tracking_service_1 = require("../common/services/tracking.service");
const question_usage_service_1 = require("../question-usage/question-usage.service");
const create_question_paper_dto_1 = require("./dto/create-question-paper.dto");
const fs = require("fs");
const path = require("path");
const PDFDocument = require("pdfkit");
const docx_1 = require("docx");
let QuestionPapersService = QuestionPapersService_1 = class QuestionPapersService {
    constructor(questionPaperModel, questionModel, subjectModel, collegeModel, trackingService, questionUsageService) {
        this.questionPaperModel = questionPaperModel;
        this.questionModel = questionModel;
        this.subjectModel = subjectModel;
        this.collegeModel = collegeModel;
        this.trackingService = trackingService;
        this.questionUsageService = questionUsageService;
        this.logger = new common_1.Logger(QuestionPapersService_1.name);
    }
    async createUnified(createQuestionPaperDto, user) {
        try {
            if (user.role !== 'teacher' && user.role !== 'superAdmin') {
                throw new common_1.BadRequestException('Only teachers and super admins can create question papers.');
            }
            if (user.role === 'teacher' && !user.collegeId) {
                throw new common_1.BadRequestException('Teacher must be associated with a college');
            }
            createQuestionPaperDto.bypassStatusChecks = true;
            const isMultiSubject = createQuestionPaperDto.subjects && createQuestionPaperDto.subjects.length > 0;
            if (isMultiSubject) {
                if (createQuestionPaperDto.subjects.length < 2) {
                    throw new common_1.BadRequestException('Multi-subject papers must contain at least 2 subjects');
                }
                if (!createQuestionPaperDto.duration) {
                    throw new common_1.BadRequestException('Duration is required for multi-subject papers');
                }
                if (createQuestionPaperDto.includeAnswers === undefined) {
                    throw new common_1.BadRequestException('includeAnswers is required for multi-subject papers');
                }
                const subjectNames = createQuestionPaperDto.subjects.map(s => s.subject);
                const uniqueSubjects = new Set(subjectNames);
                if (uniqueSubjects.size !== subjectNames.length) {
                    throw new common_1.BadRequestException('Duplicate subjects are not allowed in multi-subject papers');
                }
                for (const subjectConfig of createQuestionPaperDto.subjects) {
                    if (subjectConfig.numberOfQuestions <= 0) {
                        throw new common_1.BadRequestException(`Number of questions must be greater than 0 for subject: ${subjectConfig.subject}`);
                    }
                    if (subjectConfig.totalMarks <= 0) {
                        throw new common_1.BadRequestException(`Total marks must be greater than 0 for subject: ${subjectConfig.subject}`);
                    }
                }
            }
            else {
                if (!createQuestionPaperDto.subject) {
                    throw new common_1.BadRequestException('Subject is required for single subject papers');
                }
                if (!createQuestionPaperDto.customise) {
                    throw new common_1.BadRequestException('Only custom question paper generation is allowed. Please provide customise configuration.');
                }
                if (createQuestionPaperDto.subjects && createQuestionPaperDto.subjects.length > 0) {
                    throw new common_1.BadRequestException('Cannot provide both subject and subjects fields. Use subject for single-subject or subjects for multi-subject papers.');
                }
            }
            let collegeInfo = null;
            if (user.collegeId) {
                collegeInfo = await this.collegeModel.findById(user.collegeId).exec();
                if (!collegeInfo) {
                    throw new common_1.BadRequestException('College not found');
                }
            }
            if (isMultiSubject) {
                return this.createMultiSubjectUnified(createQuestionPaperDto, user, collegeInfo);
            }
            else {
                return this.createSingleSubjectUnified(createQuestionPaperDto, user, collegeInfo);
            }
        }
        catch (error) {
            this.logger.error(`Error creating unified question paper: ${error.message}`, error.stack);
            throw error;
        }
    }
    async createSingleSubjectUnified(createQuestionPaperDto, user, collegeInfo) {
        const subject = await this.resolveSubject(createQuestionPaperDto.subject);
        if (!subject) {
            throw new common_1.BadRequestException('Invalid subject provided');
        }
        await this.checkGenerationLimits(user, subject._id.toString());
        const questionQuery = {
            subjectId: subject._id,
        };
        if (!createQuestionPaperDto.bypassStatusChecks) {
            questionQuery.status = 'active';
            questionQuery.reviewStatus = 'approved';
        }
        if (createQuestionPaperDto.topicId) {
            questionQuery.topicId = createQuestionPaperDto.topicId;
        }
        const availableQuestions = await this.questionModel.find(questionQuery);
        if (availableQuestions.length === 0) {
            throw new common_1.BadRequestException('No questions available for the specified criteria');
        }
        const unusedQuestions = await this.filterUnusedQuestions(availableQuestions, user, subject._id.toString(), createQuestionPaperDto.topicId?.toString());
        if (unusedQuestions.length === 0) {
            throw new common_1.BadRequestException('No unused questions available. Please add more questions or wait for the duplicate prevention period to expire.');
        }
        const customConfig = createQuestionPaperDto.customise;
        if (unusedQuestions.length < customConfig.numberOfQuestions) {
            throw new common_1.BadRequestException(`Only ${unusedQuestions.length} unused questions available. Requested: ${customConfig.numberOfQuestions}`);
        }
        const selectedQuestions = await this.selectQuestionsByDifficulty(unusedQuestions, customConfig.numberOfQuestions, 'custom', customConfig.customDifficulty);
        const sections = [
            {
                name: 'Section A',
                description: 'All Questions',
                order: 1,
                sectionMarks: customConfig.totalMarks,
                questions: selectedQuestions.map((q, index) => ({
                    questionId: q._id,
                    order: index + 1,
                })),
            },
        ];
        const totalMarks = customConfig.totalMarks;
        const duration = customConfig.duration;
        const withAnswers = customConfig.includeAnswers;
        const questionPaperData = {
            title: createQuestionPaperDto.title,
            description: createQuestionPaperDto.description,
            subjectId: subject._id,
            topicId: createQuestionPaperDto.topicId,
            totalMarks,
            duration,
            withAnswers,
            instructions: createQuestionPaperDto.instructions,
            sections,
            questions: selectedQuestions.map((q) => q._id),
            generatedBy: user._id,
            status: 'active',
            examType: createQuestionPaperDto.examType || create_question_paper_dto_1.ExamType.CUSTOM,
            difficultyMode: 'custom',
        };
        if (user.collegeId) {
            questionPaperData.collegeId = user.collegeId;
        }
        const questionPaper = new this.questionPaperModel(questionPaperData);
        const savedPaper = await questionPaper.save();
        if (user.role === 'teacher' && user.collegeId) {
            await this.recordQuestionUsage(savedPaper, selectedQuestions, user);
        }
        return this.populateAndFormatResponse(savedPaper, collegeInfo);
    }
    async createMultiSubjectUnified(createQuestionPaperDto, user, collegeInfo) {
        for (const subjectConfig of createQuestionPaperDto.subjects) {
            const { easyPercentage, mediumPercentage, hardPercentage } = subjectConfig.customDifficulty;
            if (easyPercentage + mediumPercentage + hardPercentage !== 100) {
                throw new common_1.BadRequestException(`Difficulty percentages for subject ${subjectConfig.subject} must sum to 100`);
            }
        }
        const sections = [];
        const allSelectedQuestions = [];
        let totalMarks = 0;
        let questionOrder = 1;
        for (let i = 0; i < createQuestionPaperDto.subjects.length; i++) {
            const subjectConfig = createQuestionPaperDto.subjects[i];
            const subject = await this.resolveSubject(subjectConfig.subject);
            if (!subject) {
                throw new common_1.BadRequestException(`Invalid subject provided: ${subjectConfig.subject}`);
            }
            await this.checkGenerationLimits(user, subject._id.toString());
            const questionQuery = {
                subjectId: subject._id,
            };
            if (!createQuestionPaperDto.bypassStatusChecks) {
                questionQuery.status = 'active';
                questionQuery.reviewStatus = 'approved';
            }
            if (subjectConfig.topicId) {
                questionQuery.topicId = subjectConfig.topicId;
            }
            const availableQuestions = await this.questionModel.find(questionQuery);
            if (availableQuestions.length === 0) {
                throw new common_1.BadRequestException(`No questions available for subject: ${subjectConfig.subject}`);
            }
            const unusedQuestions = await this.filterUnusedQuestions(availableQuestions, user, subject._id.toString(), subjectConfig.topicId?.toString());
            if (unusedQuestions.length < subjectConfig.numberOfQuestions) {
                throw new common_1.BadRequestException(`Only ${unusedQuestions.length} unused questions available for ${subjectConfig.subject}. Requested: ${subjectConfig.numberOfQuestions}`);
            }
            const selectedQuestions = await this.selectQuestionsByDifficulty(unusedQuestions, subjectConfig.numberOfQuestions, 'custom', subjectConfig.customDifficulty);
            const sectionName = `Section ${String.fromCharCode(65 + i)}`;
            const section = {
                name: sectionName,
                description: `${subject.name} Questions (${subjectConfig.numberOfQuestions} questions, ${subjectConfig.totalMarks} marks)`,
                order: i + 1,
                sectionMarks: subjectConfig.totalMarks,
                subjectId: subject._id,
                subjectName: subject.name,
                questions: selectedQuestions.map((q) => ({
                    questionId: q._id,
                    order: questionOrder++,
                })),
            };
            sections.push(section);
            allSelectedQuestions.push(...selectedQuestions);
            totalMarks += subjectConfig.totalMarks;
        }
        const subjectNames = createQuestionPaperDto.subjects.map(s => s.subject).join(', ');
        const questionPaperData = {
            title: createQuestionPaperDto.title,
            description: createQuestionPaperDto.description || `Multi-subject question paper with ${createQuestionPaperDto.subjects.length} subjects: ${subjectNames}`,
            totalMarks,
            duration: createQuestionPaperDto.duration,
            withAnswers: createQuestionPaperDto.includeAnswers,
            instructions: createQuestionPaperDto.instructions,
            sections,
            questions: allSelectedQuestions.map((q) => q._id),
            generatedBy: user._id,
            status: 'active',
            examType: createQuestionPaperDto.examType || create_question_paper_dto_1.ExamType.CUSTOM,
            difficultyMode: 'custom',
            isMultiSubject: true,
            subjectCount: createQuestionPaperDto.subjects.length,
            subjectBreakdown: createQuestionPaperDto.subjects.map(s => ({
                subject: s.subject,
                questionCount: s.numberOfQuestions,
                marks: s.totalMarks
            })),
        };
        if (user.collegeId) {
            questionPaperData.collegeId = user.collegeId;
        }
        const questionPaper = new this.questionPaperModel(questionPaperData);
        const savedPaper = await questionPaper.save();
        if (user.role === 'teacher' && user.collegeId) {
            await this.recordQuestionUsage(savedPaper, allSelectedQuestions, user);
        }
        return this.populateAndFormatResponse(savedPaper, collegeInfo);
    }
    async populateAndFormatResponse(savedPaper, collegeInfo) {
        const populatedPaper = await this.questionPaperModel
            .findById(savedPaper._id)
            .populate('subjectId', 'name description')
            .populate('topicId', 'name description')
            .populate('questions', 'content options answer difficulty type marks explanation imageUrls')
            .exec();
        if (populatedPaper && populatedPaper.sections) {
            populatedPaper.sections = populatedPaper.sections.map(section => {
                const mappedQuestions = section.questions.map(sectionQuestion => {
                    const fullQuestion = populatedPaper.questions.find(q => {
                        const qId = q && typeof q === 'object' && '_id' in q ? q._id : q;
                        const qIdStr = qId ? qId.toString() : '';
                        const sectionQuestionIdStr = sectionQuestion.questionId ? sectionQuestion.questionId.toString() : '';
                        return qIdStr === sectionQuestionIdStr;
                    });
                    return {
                        questionId: sectionQuestion.questionId,
                        order: sectionQuestion.order,
                        question: fullQuestion
                    };
                });
                return {
                    ...section,
                    questions: mappedQuestions
                };
            });
        }
        const response = {
            questionPaper: populatedPaper,
        };
        if (collegeInfo) {
            response.college = {
                name: collegeInfo.name,
                logoUrl: collegeInfo.logoUrl,
                address: collegeInfo.address,
            };
        }
        this.logger.log(`Question paper created: ${savedPaper.title} (ID: ${savedPaper._id})`);
        return response;
    }
    async findAll(user) {
        let query = {};
        const populateFields = ['subjectId', 'topicId', 'questions'];
        switch (user.role) {
            case 'superAdmin':
                query = {};
                populateFields.push('collegeId', 'generatedBy');
                break;
            case 'collegeAdmin':
                query = { collegeId: user.collegeId };
                populateFields.push('generatedBy');
                break;
            case 'teacher':
                query = { generatedBy: user._id };
                break;
            default:
                throw new common_1.BadRequestException('Invalid user role');
        }
        const questionPapers = await this.questionPaperModel
            .find(query)
            .populate(populateFields)
            .sort({ createdAt: -1 })
            .exec();
        return questionPapers.map(questionPaper => {
            if (questionPaper && questionPaper.sections) {
                questionPaper.sections = questionPaper.sections.map(section => {
                    const mappedQuestions = section.questions.map(sectionQuestion => {
                        const fullQuestion = questionPaper.questions.find(q => {
                            const qId = q && typeof q === 'object' && '_id' in q ? q._id : q;
                            const qIdStr = qId ? qId.toString() : '';
                            const sectionQuestionIdStr = sectionQuestion.questionId ? sectionQuestion.questionId.toString() : '';
                            return qIdStr === sectionQuestionIdStr;
                        });
                        return {
                            questionId: sectionQuestion.questionId,
                            order: sectionQuestion.order,
                            question: fullQuestion
                        };
                    });
                    return {
                        ...section,
                        questions: mappedQuestions
                    };
                });
            }
            return questionPaper;
        });
    }
    async findOne(id, user) {
        const query = { _id: id };
        const populateFields = ['subjectId', 'topicId', 'questions'];
        switch (user.role) {
            case 'superAdmin':
                populateFields.push('collegeId', 'generatedBy');
                break;
            case 'collegeAdmin':
                query.collegeId = user.collegeId;
                populateFields.push('collegeId', 'generatedBy');
                break;
            case 'teacher':
                query.generatedBy = user._id;
                populateFields.push('collegeId');
                break;
            default:
                throw new common_1.BadRequestException('Invalid user role');
        }
        const questionPaper = await this.questionPaperModel
            .findOne(query)
            .populate(populateFields)
            .exec();
        if (!questionPaper) {
            throw new common_1.NotFoundException(`Question paper with ID ${id} not found or access denied`);
        }
        if (questionPaper && questionPaper.sections) {
            questionPaper.sections = questionPaper.sections.map(section => {
                const mappedQuestions = section.questions.map(sectionQuestion => {
                    const fullQuestion = questionPaper.questions.find(q => {
                        const qId = q && typeof q === 'object' && '_id' in q ? q._id : q;
                        const qIdStr = qId ? qId.toString() : '';
                        const sectionQuestionIdStr = sectionQuestion.questionId ? sectionQuestion.questionId.toString() : '';
                        return qIdStr === sectionQuestionIdStr;
                    });
                    return {
                        questionId: sectionQuestion.questionId,
                        order: sectionQuestion.order,
                        question: fullQuestion
                    };
                });
                return {
                    ...section,
                    questions: mappedQuestions
                };
            });
        }
        const response = {
            questionPaper: questionPaper,
        };
        if (questionPaper.collegeId && typeof questionPaper.collegeId === 'object') {
            const college = questionPaper.collegeId;
            response.college = {
                name: college.name,
                logoUrl: college.logoUrl,
                address: college.address,
            };
        }
        return response;
    }
    async update(id, updateQuestionPaperDto, user) {
        const questionPaper = await this.findOne(id, user);
        if (user.role === 'teacher') {
            const allowedFields = ['title'];
            const updateData = {};
            for (const field of allowedFields) {
                if (updateQuestionPaperDto[field] !== undefined) {
                    updateData[field] = updateQuestionPaperDto[field];
                }
            }
            if (Object.keys(updateData).length === 0) {
                throw new common_1.BadRequestException('Teachers can only update the title of question papers');
            }
            return await this.questionPaperModel
                .findByIdAndUpdate(id, updateData, { new: true })
                .exec();
        }
        return await this.questionPaperModel
            .findByIdAndUpdate(id, updateQuestionPaperDto, { new: true })
            .exec();
    }
    async checkDownloadLimits(user, questionPaper) {
        if (user.role === 'superAdmin') {
            return;
        }
        if (user.collegeId) {
            let limitDoc = null;
            if (questionPaper.subjectId) {
                limitDoc = await this.questionPaperModel
                    .findOne({
                    collegeId: user.collegeId,
                    subjectId: questionPaper.subjectId,
                    type: 'limit',
                })
                    .exec();
            }
            if (!limitDoc) {
                limitDoc = await this.questionPaperModel
                    .findOne({
                    collegeId: user.collegeId,
                    type: 'limit',
                    subjectId: { $exists: false },
                })
                    .exec();
            }
            if (limitDoc?.maxDownloads) {
                const downloadStats = await this.trackingService.getTeacherDownloadStats(user._id, {
                    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
                    endDate: new Date(),
                });
                const totalDownloads = downloadStats.reduce((sum, stat) => sum + stat.totalDownloads, 0);
                if (totalDownloads >= limitDoc.maxDownloads) {
                    throw new common_1.BadRequestException(`You have reached the maximum number of downloads (${limitDoc.maxDownloads}) allowed for this month`);
                }
            }
        }
    }
    async download(id, format = 'pdf', user) {
        try {
            const questionPaper = await this.findOne(id, user);
            if (user) {
                await this.checkDownloadLimits(user, questionPaper);
            }
            const uploadsDir = path.join(process.cwd(), 'uploads');
            if (!fs.existsSync(uploadsDir)) {
                fs.mkdirSync(uploadsDir, { recursive: true });
            }
            const fileName = `${questionPaper.title.replace(/\s+/g, '_')}_${Date.now()}.${format}`;
            const filePath = path.join(uploadsDir, fileName);
            if (format === 'pdf') {
                await this.generatePDF(questionPaper, filePath);
            }
            else if (format === 'docx') {
                await this.generateDOCX(questionPaper, filePath);
            }
            else {
                throw new common_1.BadRequestException(`Unsupported format: ${format}`);
            }
            this.logger.log(`Generated ${format.toUpperCase()} file at: ${filePath}`);
            return filePath;
        }
        catch (error) {
            this.logger.error(`Error generating ${format} file: ${error.message}`, error.stack);
            throw error;
        }
    }
    async setQuestionLimit(setQuestionLimitDto) {
        const updateData = {};
        if (setQuestionLimitDto.maxQuestions) {
            updateData.maxQuestions = setQuestionLimitDto.maxQuestions;
        }
        if (setQuestionLimitDto.maxGeneration) {
            updateData.maxGeneration = setQuestionLimitDto.maxGeneration;
        }
        if (setQuestionLimitDto.maxDownloads) {
            updateData.maxDownloads = setQuestionLimitDto.maxDownloads;
        }
        const query = {
            collegeId: setQuestionLimitDto.collegeId,
            type: 'limit',
        };
        if (setQuestionLimitDto.subjectId) {
            query.subjectId = setQuestionLimitDto.subjectId;
        }
        await this.questionPaperModel
            .updateOne(query, { $set: updateData }, { upsert: true })
            .exec();
    }
    isBase64Image(str) {
        if (!str || typeof str !== 'string')
            return false;
        const dataUrlPattern = /^data:image\/(png|jpg|jpeg|gif|webp|svg\+xml);base64,/i;
        if (dataUrlPattern.test(str))
            return true;
        const base64Pattern = /^[A-Za-z0-9+/]*={0,2}$/;
        return str.length > 100 && base64Pattern.test(str);
    }
    extractImagesFromText(text, addPlaceholders = true) {
        if (!text)
            return { cleanText: text, images: [] };
        const images = [];
        let cleanText = text;
        let imageCounter = 0;
        const dataUrlPattern = /data:image\/([^;]+);base64,([A-Za-z0-9+/]+=*)/g;
        let match;
        while ((match = dataUrlPattern.exec(text)) !== null) {
            const [fullMatch, format, base64Data] = match;
            try {
                const buffer = Buffer.from(base64Data, 'base64');
                images.push({
                    id: `image_${imageCounter++}`,
                    data: buffer,
                    format: format.toLowerCase()
                });
                if (addPlaceholders) {
                    cleanText = cleanText.replace(fullMatch, `[Image ${imageCounter}]`);
                }
                else {
                    cleanText = cleanText.replace(fullMatch, '');
                }
            }
            catch (error) {
                this.logger.warn(`Failed to process base64 image: ${error.message}`);
            }
        }
        const base64Pattern = /[A-Za-z0-9+/]{100,}={0,2}/g;
        const base64Matches = cleanText.match(base64Pattern);
        if (base64Matches) {
            base64Matches.forEach((base64String) => {
                if (this.isBase64Image(base64String)) {
                    try {
                        const buffer = Buffer.from(base64String, 'base64');
                        let format = 'jpeg';
                        if (buffer[0] === 0x89 && buffer[1] === 0x50)
                            format = 'png';
                        else if (buffer[0] === 0xFF && buffer[1] === 0xD8)
                            format = 'jpeg';
                        else if (buffer[0] === 0x47 && buffer[1] === 0x49)
                            format = 'gif';
                        images.push({
                            id: `image_${imageCounter++}`,
                            data: buffer,
                            format
                        });
                        if (addPlaceholders) {
                            cleanText = cleanText.replace(base64String, `[Image ${imageCounter}]`);
                        }
                        else {
                            cleanText = cleanText.replace(base64String, '');
                        }
                    }
                    catch (error) {
                        this.logger.warn(`Failed to process standalone base64 image: ${error.message}`);
                    }
                }
            });
        }
        cleanText = cleanText.replace(/\s+/g, ' ').trim();
        return { cleanText, images };
    }
    async generatePDF(questionPaper, filePath) {
        return new Promise(async (resolve, reject) => {
            try {
                const doc = new PDFDocument();
                const stream = fs.createWriteStream(filePath);
                doc.pipe(stream);
                const addHeader = async (subjectName) => {
                    try {
                        const medicosLogoPath = path.join(__dirname, '../../assets/medicos-logo.svg');
                        if (fs.existsSync(medicosLogoPath)) {
                            doc.save();
                            doc.opacity(0.1);
                            doc.image(medicosLogoPath, doc.page.width - 150, 50, { width: 100 });
                            doc.restore();
                        }
                    }
                    catch (error) {
                        this.logger.warn(`Failed to add Medicos logo: ${error.message}`);
                    }
                    if (questionPaper.collegeId && typeof questionPaper.collegeId === 'object') {
                        const college = questionPaper.collegeId;
                        if (college.logoUrl) {
                            try {
                                doc.image(college.logoUrl, 50, 50, { width: 60, height: 60 });
                            }
                            catch (error) {
                                this.logger.warn(`Failed to add college logo: ${error.message}`);
                            }
                        }
                        if (college.name) {
                            doc.fontSize(16).font('Times-Bold').text(college.name, { align: 'center' });
                            doc.moveDown(0.5);
                        }
                        if (college.address) {
                            doc.fontSize(10).font('Times-Roman').text(college.address, { align: 'center' });
                            doc.moveDown(0.5);
                        }
                    }
                    doc.fontSize(18).font('Times-Bold').text(questionPaper.title, { align: 'center' });
                    doc.moveDown(0.5);
                    if (subjectName) {
                        doc.fontSize(16).font('Times-Bold').text(`Subject: ${subjectName}`, { align: 'center' });
                        doc.moveDown(0.5);
                    }
                    else if (questionPaper.subjectId &&
                        typeof questionPaper.subjectId === 'object' &&
                        'name' in questionPaper.subjectId) {
                        doc.fontSize(16).font('Times-Bold').text(`Subject: ${questionPaper.subjectId.name}`, { align: 'center' });
                        doc.moveDown(0.5);
                    }
                    doc.fontSize(12).font('Times-Roman');
                    doc.text(`Duration: ${questionPaper.duration} minutes`, { align: 'center' });
                    doc.text(`Total Marks: ${questionPaper.totalMarks}`, { align: 'center' });
                    doc.moveDown();
                    if (questionPaper.instructions) {
                        doc.fontSize(12).font('Times-Bold').text('Instructions:', { underline: true });
                        doc.fontSize(10).font('Times-Roman').text(questionPaper.instructions);
                        doc.moveDown();
                    }
                    doc.moveTo(50, doc.y).lineTo(doc.page.width - 50, doc.y).stroke();
                    doc.moveDown();
                };
                const isMultiSubject = questionPaper.sections && questionPaper.sections.length > 1;
                if (isMultiSubject) {
                    for (let sectionIndex = 0; sectionIndex < questionPaper.sections.length; sectionIndex++) {
                        const section = questionPaper.sections[sectionIndex];
                        if (sectionIndex > 0) {
                            doc.addPage();
                        }
                        await addHeader(section.subjectName || section.name || `Subject ${sectionIndex + 1}`);
                        if (section.questions && section.questions.length > 0) {
                            this.addQuestionsToPage(doc, section.questions, questionPaper.withAnswers);
                        }
                    }
                }
                else {
                    await addHeader();
                    if (questionPaper.questions && questionPaper.questions.length > 0) {
                        this.addQuestionsToPage(doc, questionPaper.questions, questionPaper.withAnswers);
                    }
                }
                doc.end();
                stream.on('finish', () => {
                    resolve();
                });
                stream.on('error', (err) => {
                    reject(err);
                });
            }
            catch (error) {
                reject(error);
            }
        });
    }
    addQuestionsToPage(doc, questions, withAnswers) {
        questions.forEach((question, index) => {
            if (doc.y > doc.page.height - 100) {
                doc.addPage();
            }
            const questionResult = this.extractImagesFromText(question.content, false);
            doc.fontSize(11).font('Times-Roman').text(`${index + 1}. ${questionResult.cleanText}`);
            questionResult.images.forEach((image) => {
                try {
                    doc.moveDown(0.5);
                    if (doc.y + 80 > doc.page.height - 50) {
                        doc.addPage();
                    }
                    const currentY = doc.y;
                    doc.image(image.data, doc.x, currentY, {
                        fit: [120, 60]
                    });
                    doc.y = currentY + 70;
                    doc.moveDown(0.5);
                }
                catch (error) {
                    this.logger.warn(`Failed to add question image: ${error.message}`);
                    doc.fontSize(8).fillColor('red').text(`[Image could not be displayed]`).fillColor('black');
                }
            });
            if (question.imageUrls && Array.isArray(question.imageUrls) && question.imageUrls.length > 0) {
                question.imageUrls.forEach((imageUrl) => {
                    if (this.isBase64Image(imageUrl)) {
                        try {
                            const imageResult = this.extractImagesFromText(imageUrl, false);
                            imageResult.images.forEach((image) => {
                                doc.moveDown(0.5);
                                if (doc.y + 80 > doc.page.height - 50) {
                                    doc.addPage();
                                }
                                const currentY = doc.y;
                                doc.image(image.data, doc.x, currentY, {
                                    fit: [120, 60]
                                });
                                doc.y = currentY + 70;
                                doc.moveDown(0.5);
                            });
                        }
                        catch (error) {
                            this.logger.warn(`Failed to add imageUrl image: ${error.message}`);
                            doc.fontSize(8).fillColor('red').text(`[Image could not be displayed]`).fillColor('black');
                        }
                    }
                    else {
                        doc.fontSize(8).fillColor('blue').text(`Image: ${imageUrl}`).fillColor('black');
                        doc.moveDown(0.2);
                    }
                });
            }
            if (question.options &&
                Array.isArray(question.options) &&
                question.options.length > 0) {
                doc.moveDown(0.5);
                question.options.forEach((option, optIndex) => {
                    const optionLabel = String.fromCharCode(97 + optIndex);
                    const optionResult = this.extractImagesFromText(option, false);
                    doc.fontSize(10).font('Times-Roman').text(`    ${optionLabel}) ${optionResult.cleanText}`);
                    optionResult.images.forEach((image) => {
                        try {
                            doc.moveDown(0.3);
                            if (doc.y + 70 > doc.page.height - 50) {
                                doc.addPage();
                            }
                            const currentX = doc.x + 20;
                            const currentY = doc.y;
                            doc.image(image.data, currentX, currentY, {
                                fit: [100, 50]
                            });
                            doc.y = currentY + 60;
                            doc.moveDown(0.3);
                        }
                        catch (error) {
                            this.logger.warn(`Failed to add option image: ${error.message}`);
                            doc.fontSize(8).fillColor('red').text(`        [Image could not be displayed]`).fillColor('black');
                        }
                    });
                });
            }
            if (withAnswers && question.answer) {
                doc.moveDown(0.3);
                const answerResult = this.extractImagesFromText(question.answer);
                doc
                    .fillColor('black')
                    .fontSize(10)
                    .font('Times-Bold')
                    .text(`    Answer: ${answerResult.cleanText}`)
                    .font('Times-Roman')
                    .fillColor('black');
                answerResult.images.forEach((image) => {
                    try {
                        doc.moveDown(0.3);
                        if (doc.y + 70 > doc.page.height - 50) {
                            doc.addPage();
                        }
                        const currentX = doc.x + 20;
                        const currentY = doc.y;
                        doc.image(image.data, currentX, currentY, {
                            fit: [100, 50]
                        });
                        doc.y = currentY + 60;
                        doc.moveDown(0.3);
                    }
                    catch (error) {
                        this.logger.warn(`Failed to add answer image: ${error.message}`);
                        doc.fontSize(8).fillColor('red').text(`        [Image could not be displayed]`).fillColor('black');
                    }
                });
            }
            doc.moveDown();
        });
    }
    async generateDOCX(questionPaper, filePath) {
        try {
            const sections = [];
            sections.push({
                properties: {},
                children: [
                    new docx_1.Paragraph({
                        text: questionPaper.title,
                        heading: docx_1.HeadingLevel.HEADING_1,
                        alignment: docx_1.AlignmentType.CENTER,
                    }),
                ],
            });
            if (questionPaper.subjectId &&
                typeof questionPaper.subjectId === 'object' &&
                'name' in questionPaper.subjectId) {
                sections[0].children.push(new docx_1.Paragraph({
                    text: `Subject: ${questionPaper.subjectId.name}`,
                    alignment: docx_1.AlignmentType.CENTER,
                }));
            }
            else if (questionPaper.isMultiSubject && questionPaper.sections) {
                const subjectInfo = questionPaper.sections
                    .map((section) => section.subjectName || section.name)
                    .filter(Boolean)
                    .join(', ');
                if (subjectInfo) {
                    sections[0].children.push(new docx_1.Paragraph({
                        text: `Subjects: ${subjectInfo}`,
                        alignment: docx_1.AlignmentType.CENTER,
                    }));
                }
            }
            sections[0].children.push(new docx_1.Paragraph({
                text: `Duration: ${questionPaper.duration} minutes | Total Marks: ${questionPaper.totalMarks}`,
                alignment: docx_1.AlignmentType.LEFT,
                spacing: {
                    before: 400,
                    after: 200,
                },
            }));
            if (questionPaper.instructions) {
                sections[0].children.push(new docx_1.Paragraph({
                    text: 'Instructions:',
                    heading: docx_1.HeadingLevel.HEADING_2,
                }), new docx_1.Paragraph({
                    text: questionPaper.instructions,
                    spacing: {
                        after: 200,
                    },
                }));
            }
            sections[0].children.push(new docx_1.Paragraph({
                text: 'Questions:',
                heading: docx_1.HeadingLevel.HEADING_2,
                spacing: {
                    before: 200,
                    after: 200,
                },
            }));
            questionPaper.questions.forEach((question, index) => {
                const questionResult = this.extractImagesFromText(question.content);
                sections[0].children.push(new docx_1.Paragraph({
                    text: `${index + 1}. ${questionResult.cleanText}`,
                    spacing: {
                        before: 200,
                        after: 100,
                    },
                }));
                if (questionResult.images.length > 0) {
                    sections[0].children.push(new docx_1.Paragraph({
                        text: `    [${questionResult.images.length} image(s) - see PDF version for images]`,
                        spacing: {
                            after: 100,
                        },
                    }));
                }
                if (question.options &&
                    Array.isArray(question.options) &&
                    question.options.length > 0) {
                    question.options.forEach((option, optIndex) => {
                        const optionLabel = String.fromCharCode(97 + optIndex);
                        const optionResult = this.extractImagesFromText(option);
                        sections[0].children.push(new docx_1.Paragraph({
                            text: `    ${optionLabel}) ${optionResult.cleanText}`,
                            indent: {
                                left: 720,
                            },
                        }));
                        if (optionResult.images.length > 0) {
                            sections[0].children.push(new docx_1.Paragraph({
                                text: `        [${optionResult.images.length} image(s) - see PDF version for images]`,
                                indent: {
                                    left: 720,
                                },
                            }));
                        }
                    });
                }
                if (questionPaper.withAnswers && question.answer) {
                    const answerResult = this.extractImagesFromText(question.answer, false);
                    sections[0].children.push(new docx_1.Paragraph({
                        children: [
                            new docx_1.TextRun({
                                text: `    Answer: ${answerResult.cleanText}`,
                                color: '0000FF',
                                bold: true,
                            }),
                        ],
                        indent: {
                            left: 720,
                        },
                    }));
                    if (answerResult.images.length > 0) {
                        sections[0].children.push(new docx_1.Paragraph({
                            text: `        [${answerResult.images.length} image(s) - see PDF version for images]`,
                            indent: {
                                left: 720,
                            },
                        }));
                    }
                }
            });
            const doc = new docx_1.Document({
                sections,
            });
            const buffer = await docx_1.Packer.toBuffer(doc);
            fs.writeFileSync(filePath, buffer);
        }
        catch (error) {
            this.logger.error(`Error generating DOCX: ${error.message}`, error.stack);
            throw error;
        }
    }
    async resolveSubject(subjectInput) {
        if (mongoose_2.Types.ObjectId.isValid(subjectInput)) {
            const subject = await this.subjectModel.findById(subjectInput);
            if (subject)
                return subject;
        }
        const subject = await this.findSubjectByShortCode(subjectInput);
        if (subject)
            return subject;
        const exactMatch = await this.subjectModel.findOne({
            name: { $regex: new RegExp(`^${subjectInput}$`, 'i') },
        });
        if (exactMatch)
            return exactMatch;
        return null;
    }
    async findSubjectByShortCode(shortCode) {
        const subjectMappings = {
            [create_question_paper_dto_1.SubjectShortCode.PHYSICS]: ['physics', 'phy'],
            [create_question_paper_dto_1.SubjectShortCode.CHEMISTRY]: ['chemistry', 'chem'],
            [create_question_paper_dto_1.SubjectShortCode.BIOLOGY]: ['biology', 'bio'],
            [create_question_paper_dto_1.SubjectShortCode.MATHEMATICS]: ['mathematics', 'math'],
            [create_question_paper_dto_1.SubjectShortCode.MATH]: ['mathematics', 'math'],
            [create_question_paper_dto_1.SubjectShortCode.PHY]: ['physics', 'phy'],
            [create_question_paper_dto_1.SubjectShortCode.CHEM]: ['chemistry', 'chem'],
            [create_question_paper_dto_1.SubjectShortCode.BIO]: ['biology', 'bio'],
        };
        const searchTerms = subjectMappings[shortCode] || [shortCode];
        for (const term of searchTerms) {
            const subject = await this.subjectModel.findOne({
                name: { $regex: new RegExp(term, 'i') },
            });
            if (subject)
                return subject;
        }
        return null;
    }
    async recordQuestionUsage(questionPaper, selectedQuestions, user) {
        try {
            const usageData = selectedQuestions.map((question) => ({
                collegeId: user.collegeId,
                questionId: question._id.toString(),
                questionPaperId: questionPaper._id.toString(),
                usedBy: user._id,
                subjectId: questionPaper.subjectId ? questionPaper.subjectId.toString() : question.subjectId?.toString(),
                topicId: questionPaper.topicId?.toString() || question.topicId?.toString(),
                metadata: {
                    examType: questionPaper.examType,
                    difficulty: question.difficulty,
                    marks: question.marks || 1,
                },
            }));
            const result = await this.questionUsageService.recordMultipleQuestionUsage(usageData);
            this.logger.log(`Recorded question usage: ${result.recorded} recorded, ${result.skipped} skipped for paper ${questionPaper._id}`);
        }
        catch (error) {
            this.logger.error(`Failed to record question usage for paper ${questionPaper._id}: ${error.message}`, error.stack);
        }
    }
    async checkGenerationLimits(user, subjectId) {
        const limitDoc = await this.questionPaperModel
            .findOne({
            collegeId: user.collegeId,
            subjectId,
            type: 'limit',
        })
            .exec();
        if (limitDoc?.maxQuestions) {
            const existingPapers = await this.questionPaperModel.countDocuments({
                generatedBy: user._id,
                collegeId: user.collegeId,
                subjectId,
                type: { $ne: 'limit' },
            });
            if (existingPapers >= limitDoc.maxQuestions) {
                throw new common_1.BadRequestException(`You have reached the maximum number of question papers (${limitDoc.maxQuestions}) allowed for this subject`);
            }
        }
    }
    async filterUnusedQuestions(questions, user, subjectId, topicId) {
        if (user.role === 'superAdmin') {
            return questions;
        }
        if (user.collegeId) {
            const availableQuestionIds = questions.map((q) => q._id.toString());
            const unusedQuestionIds = await this.questionUsageService.getUnusedQuestions(user.collegeId, availableQuestionIds, {
                subjectId,
                ...(topicId && { topicId }),
            });
            return questions.filter((q) => unusedQuestionIds.includes(q._id.toString()));
        }
        const usedQuestions = await this.questionPaperModel.distinct('questions', {
            collegeId: user.collegeId,
            subjectId,
            ...(topicId && { topicId }),
            createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) },
        });
        return questions.filter((q) => !usedQuestions.map((id) => id.toString()).includes(q._id.toString()));
    }
    async selectQuestionsByDifficulty(questions, numberOfQuestions, difficultyMode, customDifficulty) {
        let easyPercentage = 30;
        let mediumPercentage = 50;
        let hardPercentage = 20;
        if (difficultyMode === 'custom' && customDifficulty) {
            easyPercentage = customDifficulty.easyPercentage;
            mediumPercentage = customDifficulty.mediumPercentage;
            hardPercentage = customDifficulty.hardPercentage;
        }
        const easyCount = Math.round((numberOfQuestions * easyPercentage) / 100);
        const mediumCount = Math.round((numberOfQuestions * mediumPercentage) / 100);
        const hardCount = numberOfQuestions - easyCount - mediumCount;
        const easyQuestions = questions.filter((q) => (q.difficulty || 'medium') === 'easy');
        const mediumQuestions = questions.filter((q) => (q.difficulty || 'medium') === 'medium');
        const hardQuestions = questions.filter((q) => (q.difficulty || 'medium') === 'hard');
        const selectedQuestions = [];
        const shuffledEasy = easyQuestions.sort(() => Math.random() - 0.5);
        selectedQuestions.push(...shuffledEasy.slice(0, Math.min(easyCount, shuffledEasy.length)));
        const shuffledMedium = mediumQuestions.sort(() => Math.random() - 0.5);
        selectedQuestions.push(...shuffledMedium.slice(0, Math.min(mediumCount, shuffledMedium.length)));
        const shuffledHard = hardQuestions.sort(() => Math.random() - 0.5);
        selectedQuestions.push(...shuffledHard.slice(0, Math.min(hardCount, shuffledHard.length)));
        if (selectedQuestions.length < numberOfQuestions) {
            const remainingQuestions = questions.filter((q) => !selectedQuestions
                .map((sq) => sq._id.toString())
                .includes(q._id.toString()));
            const shuffledRemaining = remainingQuestions.sort(() => Math.random() - 0.5);
            selectedQuestions.push(...shuffledRemaining.slice(0, numberOfQuestions - selectedQuestions.length));
        }
        return selectedQuestions.slice(0, numberOfQuestions);
    }
};
exports.QuestionPapersService = QuestionPapersService;
exports.QuestionPapersService = QuestionPapersService = QuestionPapersService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(question_paper_schema_1.QuestionPaper.name)),
    __param(1, (0, mongoose_1.InjectModel)(question_schema_1.Question.name)),
    __param(2, (0, mongoose_1.InjectModel)(subject_schema_1.Subject.name)),
    __param(3, (0, mongoose_1.InjectModel)(college_schema_1.College.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        tracking_service_1.TrackingService,
        question_usage_service_1.QuestionUsageService])
], QuestionPapersService);
//# sourceMappingURL=question-papers.service.js.map