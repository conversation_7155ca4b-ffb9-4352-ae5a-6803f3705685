"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/lib/api/analytics.ts":
/*!**********************************!*\
  !*** ./src/lib/api/analytics.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getPlatformSummary: () => (/* binding */ getPlatformSummary),\n/* harmony export */   getQuestionStats: () => (/* binding */ getQuestionStats),\n/* harmony export */   getQuestionUsage: () => (/* binding */ getQuestionUsage),\n/* harmony export */   getTopColleges: () => (/* binding */ getTopColleges)\n/* harmony export */ });\n/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils/errorHandler */ \"(app-pages-browser)/./src/lib/utils/errorHandler.ts\");\n\n\n/**\n * Get platform summary statistics\n * @returns Platform summary data\n */ const getPlatformSummary = async ()=>{\n    try {\n        return await (0,_api__WEBPACK_IMPORTED_MODULE_0__.apiCall)('/analytics/platform-summary');\n    } catch (error) {\n        console.error('Error fetching platform summary:', error);\n        throw new Error(error.message || 'Failed to fetch platform summary');\n    }\n};\n/**\n * Get top colleges by various metrics\n * @returns Top colleges data\n */ const getTopColleges = async ()=>{\n    try {\n        const result = await (0,_api__WEBPACK_IMPORTED_MODULE_0__.apiCall)('/analytics/top-colleges');\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_1__.createSuccessResponse)(result);\n    } catch (error) {\n        console.error('Error fetching top colleges:', error);\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_1__.handleApiError)(error.message || 'Failed to fetch top colleges data', 'Failed to load top colleges data. Please try again.');\n    }\n};\n/**\n * Get question usage statistics\n * @returns Question usage data\n */ const getQuestionUsage = async ()=>{\n    try {\n        const result = await (0,_api__WEBPACK_IMPORTED_MODULE_0__.apiCall)('/analytics/question-usage');\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_1__.createSuccessResponse)(result);\n    } catch (error) {\n        console.error('Error fetching question usage:', error);\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_1__.handleApiError)(error.message || 'Failed to fetch question usage data', 'Failed to load question usage data. Please try again.');\n    }\n};\n/**\n * Get question statistics\n * @returns Question statistics data\n */ const getQuestionStats = async ()=>{\n    try {\n        const result = await (0,_api__WEBPACK_IMPORTED_MODULE_0__.apiCall)('/analytics/question-stats');\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_1__.createSuccessResponse)(result);\n    } catch (error) {\n        console.error('Error fetching question stats:', error);\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_1__.handleApiError)(error.message || 'Failed to fetch question statistics', 'Failed to load question statistics. Please try again.');\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/analytics.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/utils/errorHandler.ts":
/*!***************************************!*\
  !*** ./src/lib/utils/errorHandler.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSuccessResponse: () => (/* binding */ createSuccessResponse),\n/* harmony export */   handleApiError: () => (/* binding */ handleApiError),\n/* harmony export */   isApiError: () => (/* binding */ isApiError),\n/* harmony export */   isApiSuccess: () => (/* binding */ isApiSuccess),\n/* harmony export */   safeApiCall: () => (/* binding */ safeApiCall)\n/* harmony export */ });\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n\n/**\n * Handles API errors and shows appropriate user notifications\n * @param error - The error object or message\n * @param defaultMessage - Default message to show if error message is not available\n * @param showToast - Whether to show a toast notification (default: true)\n * @returns Formatted error object\n */ function handleApiError(error) {\n    let defaultMessage = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'An error occurred. Please try again.', showToast = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;\n    var _error_response_data, _error_response, _error_data, _error_response1;\n    let errorMessage = defaultMessage;\n    let statusCode;\n    // Extract error message from different error formats\n    if (error === null || error === void 0 ? void 0 : error.message) {\n        errorMessage = error.message;\n    } else if (typeof error === 'string') {\n        errorMessage = error;\n    } else if (error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n        errorMessage = error.response.data.message;\n    } else if (error === null || error === void 0 ? void 0 : (_error_data = error.data) === null || _error_data === void 0 ? void 0 : _error_data.message) {\n        errorMessage = error.data.message;\n    }\n    // Extract status code if available\n    if (error === null || error === void 0 ? void 0 : error.status) {\n        statusCode = error.status;\n    } else if (error === null || error === void 0 ? void 0 : (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) {\n        statusCode = error.response.status;\n    }\n    // Improve error messages based on common patterns\n    if (errorMessage.includes('already exists')) {\n    // Keep the original message for duplicate entries as it's already user-friendly\n    } else if (errorMessage.includes('Authentication') || errorMessage.includes('Unauthorized')) {\n        errorMessage = 'Please log in again to continue. Your session may have expired.';\n    } else if (errorMessage.includes('Network') || errorMessage.includes('fetch')) {\n        errorMessage = 'Please check your internet connection and try again.';\n    } else if (errorMessage.includes('not found')) {\n        errorMessage = 'The requested resource was not found.';\n    } else if (errorMessage.includes('Forbidden')) {\n        errorMessage = 'You do not have permission to perform this action.';\n    } else if (statusCode === 500) {\n        errorMessage = 'Server error. Please try again later.';\n    } else if (statusCode === 503) {\n        errorMessage = 'Service temporarily unavailable. Please try again later.';\n    }\n    // Show toast notification if requested\n    if (showToast) {\n        sonner__WEBPACK_IMPORTED_MODULE_0__.toast.error(errorMessage);\n    }\n    return {\n        success: false,\n        error: errorMessage,\n        statusCode\n    };\n}\n/**\n * Creates a success response object\n * @param data - The success data\n * @param showToast - Whether to show a success toast (default: false)\n * @param successMessage - Success message to show in toast\n * @returns Success response object\n */ function createSuccessResponse(data) {\n    let showToast = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false, successMessage = arguments.length > 2 ? arguments[2] : void 0;\n    if (showToast && successMessage) {\n        sonner__WEBPACK_IMPORTED_MODULE_0__.toast.success(successMessage);\n    }\n    return {\n        success: true,\n        data\n    };\n}\n/**\n * Wrapper for API calls that handles errors consistently\n * @param apiCall - The API function to call\n * @param defaultErrorMessage - Default error message\n * @param showErrorToast - Whether to show error toast\n * @returns Promise with ApiResponse\n */ async function safeApiCall(apiCall) {\n    let defaultErrorMessage = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'Operation failed. Please try again.', showErrorToast = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;\n    try {\n        const result = await apiCall();\n        return createSuccessResponse(result);\n    } catch (error) {\n        return handleApiError(error, defaultErrorMessage, showErrorToast);\n    }\n}\n/**\n * Checks if an API response is successful\n * @param response - The API response to check\n * @returns True if successful, false otherwise\n */ function isApiSuccess(response) {\n    return response.success === true;\n}\n/**\n * Checks if an API response is an error\n * @param response - The API response to check\n * @returns True if error, false otherwise\n */ function isApiError(response) {\n    return response.success === false;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/utils/errorHandler.ts\n"));

/***/ })

});