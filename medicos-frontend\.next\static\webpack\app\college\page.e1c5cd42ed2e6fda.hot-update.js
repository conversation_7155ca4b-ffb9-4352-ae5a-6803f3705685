"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/college/page",{

/***/ "(app-pages-browser)/./node_modules/sonner/dist/index.mjs":
/*!********************************************!*\
  !*** ./node_modules/sonner/dist/index.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useSonner: () => (/* binding */ useSonner)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\n/* __next_internal_client_entry_do_not_use__ Toaster,toast,useSonner auto */ var _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\nfunction __insertCSS(code) {\n    if (!code || typeof document == 'undefined') return;\n    let head = document.head || document.getElementsByTagName('head')[0];\n    let style = document.createElement('style');\n    style.type = 'text/css';\n    head.appendChild(style);\n    style.styleSheet ? style.styleSheet.cssText = code : style.appendChild(document.createTextNode(code));\n}\n\n\nconst getAsset = (type)=>{\n    switch(type){\n        case 'success':\n            return SuccessIcon;\n        case 'info':\n            return InfoIcon;\n        case 'warning':\n            return WarningIcon;\n        case 'error':\n            return ErrorIcon;\n        default:\n            return null;\n    }\n};\nconst bars = Array(12).fill(0);\nconst Loader = (param)=>{\n    let { visible, className } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: [\n            'sonner-loading-wrapper',\n            className\n        ].filter(Boolean).join(' '),\n        \"data-visible\": visible\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: \"sonner-spinner\"\n    }, bars.map((_, i)=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: \"sonner-loading-bar\",\n            key: \"spinner-bar-\".concat(i)\n        }))));\n};\n_c = Loader;\nconst SuccessIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z\",\n    clipRule: \"evenodd\"\n}));\nconst WarningIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z\",\n    clipRule: \"evenodd\"\n}));\nconst InfoIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z\",\n    clipRule: \"evenodd\"\n}));\nconst ErrorIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z\",\n    clipRule: \"evenodd\"\n}));\nconst CloseIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n    x1: \"18\",\n    y1: \"6\",\n    x2: \"6\",\n    y2: \"18\"\n}), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n    x1: \"6\",\n    y1: \"6\",\n    x2: \"18\",\n    y2: \"18\"\n}));\nconst useIsDocumentHidden = ()=>{\n    _s();\n    const [isDocumentHidden, setIsDocumentHidden] = react__WEBPACK_IMPORTED_MODULE_0__.useState(document.hidden);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useIsDocumentHidden.useEffect\": ()=>{\n            const callback = {\n                \"useIsDocumentHidden.useEffect.callback\": ()=>{\n                    setIsDocumentHidden(document.hidden);\n                }\n            }[\"useIsDocumentHidden.useEffect.callback\"];\n            document.addEventListener('visibilitychange', callback);\n            return ({\n                \"useIsDocumentHidden.useEffect\": ()=>window.removeEventListener('visibilitychange', callback)\n            })[\"useIsDocumentHidden.useEffect\"];\n        }\n    }[\"useIsDocumentHidden.useEffect\"], []);\n    return isDocumentHidden;\n};\n_s(useIsDocumentHidden, \"RJwWklAunJjdVVAElZ/SoraKxVU=\");\nlet toastsCounter = 1;\nclass Observer {\n    constructor(){\n        // We use arrow functions to maintain the correct `this` reference\n        this.subscribe = (subscriber)=>{\n            this.subscribers.push(subscriber);\n            return ()=>{\n                const index = this.subscribers.indexOf(subscriber);\n                this.subscribers.splice(index, 1);\n            };\n        };\n        this.publish = (data)=>{\n            this.subscribers.forEach((subscriber)=>subscriber(data));\n        };\n        this.addToast = (data)=>{\n            this.publish(data);\n            this.toasts = [\n                ...this.toasts,\n                data\n            ];\n        };\n        this.create = (data)=>{\n            var _data_id;\n            const { message, ...rest } = data;\n            const id = typeof (data == null ? void 0 : data.id) === 'number' || ((_data_id = data.id) == null ? void 0 : _data_id.length) > 0 ? data.id : toastsCounter++;\n            const alreadyExists = this.toasts.find((toast)=>{\n                return toast.id === id;\n            });\n            const dismissible = data.dismissible === undefined ? true : data.dismissible;\n            if (this.dismissedToasts.has(id)) {\n                this.dismissedToasts.delete(id);\n            }\n            if (alreadyExists) {\n                this.toasts = this.toasts.map((toast)=>{\n                    if (toast.id === id) {\n                        this.publish({\n                            ...toast,\n                            ...data,\n                            id,\n                            title: message\n                        });\n                        return {\n                            ...toast,\n                            ...data,\n                            id,\n                            dismissible,\n                            title: message\n                        };\n                    }\n                    return toast;\n                });\n            } else {\n                this.addToast({\n                    title: message,\n                    ...rest,\n                    dismissible,\n                    id\n                });\n            }\n            return id;\n        };\n        this.dismiss = (id)=>{\n            if (id) {\n                this.dismissedToasts.add(id);\n                requestAnimationFrame(()=>this.subscribers.forEach((subscriber)=>subscriber({\n                            id,\n                            dismiss: true\n                        })));\n            } else {\n                this.toasts.forEach((toast)=>{\n                    this.subscribers.forEach((subscriber)=>subscriber({\n                            id: toast.id,\n                            dismiss: true\n                        }));\n                });\n            }\n            return id;\n        };\n        this.message = (message, data)=>{\n            return this.create({\n                ...data,\n                message\n            });\n        };\n        this.error = (message, data)=>{\n            return this.create({\n                ...data,\n                message,\n                type: 'error'\n            });\n        };\n        this.success = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'success',\n                message\n            });\n        };\n        this.info = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'info',\n                message\n            });\n        };\n        this.warning = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'warning',\n                message\n            });\n        };\n        this.loading = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'loading',\n                message\n            });\n        };\n        this.promise = (promise, data)=>{\n            if (!data) {\n                // Nothing to show\n                return;\n            }\n            let id = undefined;\n            if (data.loading !== undefined) {\n                id = this.create({\n                    ...data,\n                    promise,\n                    type: 'loading',\n                    message: data.loading,\n                    description: typeof data.description !== 'function' ? data.description : undefined\n                });\n            }\n            const p = Promise.resolve(promise instanceof Function ? promise() : promise);\n            let shouldDismiss = id !== undefined;\n            let result;\n            const originalPromise = p.then(async (response)=>{\n                result = [\n                    'resolve',\n                    response\n                ];\n                const isReactElementResponse = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(response);\n                if (isReactElementResponse) {\n                    shouldDismiss = false;\n                    this.create({\n                        id,\n                        type: 'default',\n                        message: response\n                    });\n                } else if (isHttpResponse(response) && !response.ok) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === 'function' ? await data.error(\"HTTP error! status: \".concat(response.status)) : data.error;\n                    const description = typeof data.description === 'function' ? await data.description(\"HTTP error! status: \".concat(response.status)) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'error',\n                        description,\n                        ...toastSettings\n                    });\n                } else if (response instanceof Error) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === 'function' ? await data.error(response) : data.error;\n                    const description = typeof data.description === 'function' ? await data.description(response) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'error',\n                        description,\n                        ...toastSettings\n                    });\n                } else if (data.success !== undefined) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.success === 'function' ? await data.success(response) : data.success;\n                    const description = typeof data.description === 'function' ? await data.description(response) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'success',\n                        description,\n                        ...toastSettings\n                    });\n                }\n            }).catch(async (error)=>{\n                result = [\n                    'reject',\n                    error\n                ];\n                if (data.error !== undefined) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === 'function' ? await data.error(error) : data.error;\n                    const description = typeof data.description === 'function' ? await data.description(error) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'error',\n                        description,\n                        ...toastSettings\n                    });\n                }\n            }).finally(()=>{\n                if (shouldDismiss) {\n                    // Toast is still in load state (and will be indefinitely — dismiss it)\n                    this.dismiss(id);\n                    id = undefined;\n                }\n                data.finally == null ? void 0 : data.finally.call(data);\n            });\n            const unwrap = ()=>new Promise((resolve, reject)=>originalPromise.then(()=>result[0] === 'reject' ? reject(result[1]) : resolve(result[1])).catch(reject));\n            if (typeof id !== 'string' && typeof id !== 'number') {\n                // cannot Object.assign on undefined\n                return {\n                    unwrap\n                };\n            } else {\n                return Object.assign(id, {\n                    unwrap\n                });\n            }\n        };\n        this.custom = (jsx, data)=>{\n            const id = (data == null ? void 0 : data.id) || toastsCounter++;\n            this.create({\n                jsx: jsx(id),\n                id,\n                ...data\n            });\n            return id;\n        };\n        this.getActiveToasts = ()=>{\n            return this.toasts.filter((toast)=>!this.dismissedToasts.has(toast.id));\n        };\n        this.subscribers = [];\n        this.toasts = [];\n        this.dismissedToasts = new Set();\n    }\n}\nconst ToastState = new Observer();\n// bind this to the toast function\nconst toastFunction = (message, data)=>{\n    const id = (data == null ? void 0 : data.id) || toastsCounter++;\n    ToastState.addToast({\n        title: message,\n        ...data,\n        id\n    });\n    return id;\n};\nconst isHttpResponse = (data)=>{\n    return data && typeof data === 'object' && 'ok' in data && typeof data.ok === 'boolean' && 'status' in data && typeof data.status === 'number';\n};\nconst basicToast = toastFunction;\nconst getHistory = ()=>ToastState.toasts;\nconst getToasts = ()=>ToastState.getActiveToasts();\n// We use `Object.assign` to maintain the correct types as we would lose them otherwise\nconst toast = Object.assign(basicToast, {\n    success: ToastState.success,\n    info: ToastState.info,\n    warning: ToastState.warning,\n    error: ToastState.error,\n    custom: ToastState.custom,\n    message: ToastState.message,\n    promise: ToastState.promise,\n    dismiss: ToastState.dismiss,\n    loading: ToastState.loading\n}, {\n    getHistory,\n    getToasts\n});\n__insertCSS(\"[data-sonner-toaster][dir=ltr],html[dir=ltr]{--toast-icon-margin-start:-3px;--toast-icon-margin-end:4px;--toast-svg-margin-start:-1px;--toast-svg-margin-end:0px;--toast-button-margin-start:auto;--toast-button-margin-end:0;--toast-close-button-start:0;--toast-close-button-end:unset;--toast-close-button-transform:translate(-35%, -35%)}[data-sonner-toaster][dir=rtl],html[dir=rtl]{--toast-icon-margin-start:4px;--toast-icon-margin-end:-3px;--toast-svg-margin-start:0px;--toast-svg-margin-end:-1px;--toast-button-margin-start:0;--toast-button-margin-end:auto;--toast-close-button-start:unset;--toast-close-button-end:0;--toast-close-button-transform:translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1:hsl(0, 0%, 99%);--gray2:hsl(0, 0%, 97.3%);--gray3:hsl(0, 0%, 95.1%);--gray4:hsl(0, 0%, 93%);--gray5:hsl(0, 0%, 90.9%);--gray6:hsl(0, 0%, 88.7%);--gray7:hsl(0, 0%, 85.8%);--gray8:hsl(0, 0%, 78%);--gray9:hsl(0, 0%, 56.1%);--gray10:hsl(0, 0%, 52.3%);--gray11:hsl(0, 0%, 43.5%);--gray12:hsl(0, 0%, 9%);--border-radius:8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:0;z-index:999999999;transition:transform .4s ease}[data-sonner-toaster][data-lifted=true]{transform:translateY(-8px)}@media (hover:none) and (pointer:coarse){[data-sonner-toaster][data-lifted=true]{transform:none}}[data-sonner-toaster][data-x-position=right]{right:var(--offset-right)}[data-sonner-toaster][data-x-position=left]{left:var(--offset-left)}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translateX(-50%)}[data-sonner-toaster][data-y-position=top]{top:var(--offset-top)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--offset-bottom)}[data-sonner-toast]{--y:translateY(100%);--lift-amount:calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:0;overflow-wrap:anywhere}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px rgba(0,0,0,.1);width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-y-position=top]{top:0;--y:translateY(-100%);--lift:1;--lift-amount:calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y:translateY(100%);--lift:-1;--lift-amount:calc(var(--lift) * var(--gap))}[data-sonner-toast][data-styled=true] [data-description]{font-weight:400;line-height:1.4;color:#3f3f3f}[data-rich-colors=true][data-sonner-toast][data-styled=true] [data-description]{color:inherit}[data-sonner-toaster][data-sonner-theme=dark] [data-description]{color:#e8e8e8}[data-sonner-toast][data-styled=true] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast][data-styled=true] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast][data-styled=true] [data-icon]>*{flex-shrink:0}[data-sonner-toast][data-styled=true] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast][data-styled=true] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;font-weight:500;cursor:pointer;outline:0;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}[data-sonner-toast][data-styled=true] [data-button]:focus-visible{box-shadow:0 0 0 2px rgba(0,0,0,.4)}[data-sonner-toast][data-styled=true] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast][data-styled=true] [data-cancel]{color:var(--normal-text);background:rgba(0,0,0,.08)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-styled=true] [data-cancel]{background:rgba(255,255,255,.3)}[data-sonner-toast][data-styled=true] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);background:var(--normal-bg);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast][data-styled=true] [data-close-button]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-styled=true] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast][data-styled=true]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]::before{content:'';position:absolute;left:-100%;right:-100%;height:100%;z-index:-1}[data-sonner-toast][data-y-position=top][data-swiping=true]::before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]::before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]::before{content:'';position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast][data-expanded=true]::after{content:'';position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y:translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale:var(--toasts-before) * 0.05 + 1;--y:translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-x-position=right]{right:0}[data-sonner-toast][data-x-position=left]{left:0}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y:translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y:translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]::before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y,0)) translateX(var(--swipe-amount-x,0));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width:600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-sonner-theme=light]{--normal-bg:#fff;--normal-border:var(--gray4);--normal-text:var(--gray12);--success-bg:hsl(143, 85%, 96%);--success-border:hsl(145, 92%, 87%);--success-text:hsl(140, 100%, 27%);--info-bg:hsl(208, 100%, 97%);--info-border:hsl(221, 91%, 93%);--info-text:hsl(210, 92%, 45%);--warning-bg:hsl(49, 100%, 97%);--warning-border:hsl(49, 91%, 84%);--warning-text:hsl(31, 92%, 45%);--error-bg:hsl(359, 100%, 97%);--error-border:hsl(359, 100%, 94%);--error-text:hsl(360, 100%, 45%)}[data-sonner-toaster][data-sonner-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg:#000;--normal-border:hsl(0, 0%, 20%);--normal-text:var(--gray1)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg:#fff;--normal-border:var(--gray3);--normal-text:var(--gray12)}[data-sonner-toaster][data-sonner-theme=dark]{--normal-bg:#000;--normal-bg-hover:hsl(0, 0%, 12%);--normal-border:hsl(0, 0%, 20%);--normal-border-hover:hsl(0, 0%, 25%);--normal-text:var(--gray1);--success-bg:hsl(150, 100%, 6%);--success-border:hsl(147, 100%, 12%);--success-text:hsl(150, 86%, 65%);--info-bg:hsl(215, 100%, 6%);--info-border:hsl(223, 43%, 17%);--info-text:hsl(216, 87%, 65%);--warning-bg:hsl(64, 100%, 6%);--warning-border:hsl(60, 100%, 9%);--warning-text:hsl(46, 87%, 65%);--error-bg:hsl(358, 76%, 10%);--error-border:hsl(357, 89%, 16%);--error-text:hsl(358, 100%, 81%)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size:16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:first-child{animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}100%{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}100%{opacity:.15}}@media (prefers-reduced-motion){.sonner-loading-bar,[data-sonner-toast],[data-sonner-toast]>*{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}\");\nfunction isAction(action) {\n    return action.label !== undefined;\n}\n// Visible toasts amount\nconst VISIBLE_TOASTS_AMOUNT = 3;\n// Viewport padding\nconst VIEWPORT_OFFSET = '24px';\n// Mobile viewport padding\nconst MOBILE_VIEWPORT_OFFSET = '16px';\n// Default lifetime of a toasts (in ms)\nconst TOAST_LIFETIME = 4000;\n// Default toast width\nconst TOAST_WIDTH = 356;\n// Default gap between toasts\nconst GAP = 14;\n// Threshold to dismiss a toast\nconst SWIPE_THRESHOLD = 45;\n// Equal to exit animation duration\nconst TIME_BEFORE_UNMOUNT = 200;\nfunction cn() {\n    for(var _len = arguments.length, classes = new Array(_len), _key = 0; _key < _len; _key++){\n        classes[_key] = arguments[_key];\n    }\n    return classes.filter(Boolean).join(' ');\n}\nfunction getDefaultSwipeDirections(position) {\n    const [y, x] = position.split('-');\n    const directions = [];\n    if (y) {\n        directions.push(y);\n    }\n    if (x) {\n        directions.push(x);\n    }\n    return directions;\n}\nconst Toast = (props)=>{\n    _s1();\n    var _toast_classNames, _toast_classNames1, _toast_classNames2, _toast_classNames3, _toast_classNames4, _toast_classNames5, _toast_classNames6, _toast_classNames7, _toast_classNames8;\n    const { invert: ToasterInvert, toast, unstyled, interacting, setHeights, visibleToasts, heights, index, toasts, expanded, removeToast, defaultRichColors, closeButton: closeButtonFromToaster, style, cancelButtonStyle, actionButtonStyle, className = '', descriptionClassName = '', duration: durationFromToaster, position, gap, expandByDefault, classNames, icons, closeButtonAriaLabel = 'Close toast' } = props;\n    const [swipeDirection, setSwipeDirection] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [swipeOutDirection, setSwipeOutDirection] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [removed, setRemoved] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [swiping, setSwiping] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [swipeOut, setSwipeOut] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [isSwiped, setIsSwiped] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [offsetBeforeRemove, setOffsetBeforeRemove] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [initialHeight, setInitialHeight] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const remainingTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(toast.duration || durationFromToaster || TOAST_LIFETIME);\n    const dragStartTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const toastRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isFront = index === 0;\n    const isVisible = index + 1 <= visibleToasts;\n    const toastType = toast.type;\n    const dismissible = toast.dismissible !== false;\n    const toastClassname = toast.className || '';\n    const toastDescriptionClassname = toast.descriptionClassName || '';\n    // Height index is used to calculate the offset as it gets updated before the toast array, which means we can calculate the new layout faster.\n    const heightIndex = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo[heightIndex]\": ()=>heights.findIndex({\n                \"Toast.useMemo[heightIndex]\": (height)=>height.toastId === toast.id\n            }[\"Toast.useMemo[heightIndex]\"]) || 0\n    }[\"Toast.useMemo[heightIndex]\"], [\n        heights,\n        toast.id\n    ]);\n    const closeButton = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo[closeButton]\": ()=>{\n            var _toast_closeButton;\n            return (_toast_closeButton = toast.closeButton) != null ? _toast_closeButton : closeButtonFromToaster;\n        }\n    }[\"Toast.useMemo[closeButton]\"], [\n        toast.closeButton,\n        closeButtonFromToaster\n    ]);\n    const duration = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo[duration]\": ()=>toast.duration || durationFromToaster || TOAST_LIFETIME\n    }[\"Toast.useMemo[duration]\"], [\n        toast.duration,\n        durationFromToaster\n    ]);\n    const closeTimerStartTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const offset = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const lastCloseTimerStartTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const pointerStartRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const [y, x] = position.split('-');\n    const toastsHeightBefore = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo[toastsHeightBefore]\": ()=>{\n            return heights.reduce({\n                \"Toast.useMemo[toastsHeightBefore]\": (prev, curr, reducerIndex)=>{\n                    // Calculate offset up until current toast\n                    if (reducerIndex >= heightIndex) {\n                        return prev;\n                    }\n                    return prev + curr.height;\n                }\n            }[\"Toast.useMemo[toastsHeightBefore]\"], 0);\n        }\n    }[\"Toast.useMemo[toastsHeightBefore]\"], [\n        heights,\n        heightIndex\n    ]);\n    const isDocumentHidden = useIsDocumentHidden();\n    const invert = toast.invert || ToasterInvert;\n    const disabled = toastType === 'loading';\n    offset.current = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo\": ()=>heightIndex * gap + toastsHeightBefore\n    }[\"Toast.useMemo\"], [\n        heightIndex,\n        toastsHeightBefore\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            remainingTime.current = duration;\n        }\n    }[\"Toast.useEffect\"], [\n        duration\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            // Trigger enter animation without using CSS animation\n            setMounted(true);\n        }\n    }[\"Toast.useEffect\"], []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            const toastNode = toastRef.current;\n            if (toastNode) {\n                const height = toastNode.getBoundingClientRect().height;\n                // Add toast height to heights array after the toast is mounted\n                setInitialHeight(height);\n                setHeights({\n                    \"Toast.useEffect\": (h)=>[\n                            {\n                                toastId: toast.id,\n                                height,\n                                position: toast.position\n                            },\n                            ...h\n                        ]\n                }[\"Toast.useEffect\"]);\n                return ({\n                    \"Toast.useEffect\": ()=>setHeights({\n                            \"Toast.useEffect\": (h)=>h.filter({\n                                    \"Toast.useEffect\": (height)=>height.toastId !== toast.id\n                                }[\"Toast.useEffect\"])\n                        }[\"Toast.useEffect\"])\n                })[\"Toast.useEffect\"];\n            }\n        }\n    }[\"Toast.useEffect\"], [\n        setHeights,\n        toast.id\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect({\n        \"Toast.useLayoutEffect\": ()=>{\n            if (!mounted) return;\n            const toastNode = toastRef.current;\n            const originalHeight = toastNode.style.height;\n            toastNode.style.height = 'auto';\n            const newHeight = toastNode.getBoundingClientRect().height;\n            toastNode.style.height = originalHeight;\n            setInitialHeight(newHeight);\n            setHeights({\n                \"Toast.useLayoutEffect\": (heights)=>{\n                    const alreadyExists = heights.find({\n                        \"Toast.useLayoutEffect.alreadyExists\": (height)=>height.toastId === toast.id\n                    }[\"Toast.useLayoutEffect.alreadyExists\"]);\n                    if (!alreadyExists) {\n                        return [\n                            {\n                                toastId: toast.id,\n                                height: newHeight,\n                                position: toast.position\n                            },\n                            ...heights\n                        ];\n                    } else {\n                        return heights.map({\n                            \"Toast.useLayoutEffect\": (height)=>height.toastId === toast.id ? {\n                                    ...height,\n                                    height: newHeight\n                                } : height\n                        }[\"Toast.useLayoutEffect\"]);\n                    }\n                }\n            }[\"Toast.useLayoutEffect\"]);\n        }\n    }[\"Toast.useLayoutEffect\"], [\n        mounted,\n        toast.title,\n        toast.description,\n        setHeights,\n        toast.id\n    ]);\n    const deleteToast = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Toast.useCallback[deleteToast]\": ()=>{\n            // Save the offset for the exit swipe animation\n            setRemoved(true);\n            setOffsetBeforeRemove(offset.current);\n            setHeights({\n                \"Toast.useCallback[deleteToast]\": (h)=>h.filter({\n                        \"Toast.useCallback[deleteToast]\": (height)=>height.toastId !== toast.id\n                    }[\"Toast.useCallback[deleteToast]\"])\n            }[\"Toast.useCallback[deleteToast]\"]);\n            setTimeout({\n                \"Toast.useCallback[deleteToast]\": ()=>{\n                    removeToast(toast);\n                }\n            }[\"Toast.useCallback[deleteToast]\"], TIME_BEFORE_UNMOUNT);\n        }\n    }[\"Toast.useCallback[deleteToast]\"], [\n        toast,\n        removeToast,\n        setHeights,\n        offset\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            if (toast.promise && toastType === 'loading' || toast.duration === Infinity || toast.type === 'loading') return;\n            let timeoutId;\n            // Pause the timer on each hover\n            const pauseTimer = {\n                \"Toast.useEffect.pauseTimer\": ()=>{\n                    if (lastCloseTimerStartTimeRef.current < closeTimerStartTimeRef.current) {\n                        // Get the elapsed time since the timer started\n                        const elapsedTime = new Date().getTime() - closeTimerStartTimeRef.current;\n                        remainingTime.current = remainingTime.current - elapsedTime;\n                    }\n                    lastCloseTimerStartTimeRef.current = new Date().getTime();\n                }\n            }[\"Toast.useEffect.pauseTimer\"];\n            const startTimer = {\n                \"Toast.useEffect.startTimer\": ()=>{\n                    // setTimeout(, Infinity) behaves as if the delay is 0.\n                    // As a result, the toast would be closed immediately, giving the appearance that it was never rendered.\n                    // See: https://github.com/denysdovhan/wtfjs?tab=readme-ov-file#an-infinite-timeout\n                    if (remainingTime.current === Infinity) return;\n                    closeTimerStartTimeRef.current = new Date().getTime();\n                    // Let the toast know it has started\n                    timeoutId = setTimeout({\n                        \"Toast.useEffect.startTimer\": ()=>{\n                            toast.onAutoClose == null ? void 0 : toast.onAutoClose.call(toast, toast);\n                            deleteToast();\n                        }\n                    }[\"Toast.useEffect.startTimer\"], remainingTime.current);\n                }\n            }[\"Toast.useEffect.startTimer\"];\n            if (expanded || interacting || isDocumentHidden) {\n                pauseTimer();\n            } else {\n                startTimer();\n            }\n            return ({\n                \"Toast.useEffect\": ()=>clearTimeout(timeoutId)\n            })[\"Toast.useEffect\"];\n        }\n    }[\"Toast.useEffect\"], [\n        expanded,\n        interacting,\n        toast,\n        toastType,\n        isDocumentHidden,\n        deleteToast\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            if (toast.delete) {\n                deleteToast();\n            }\n        }\n    }[\"Toast.useEffect\"], [\n        deleteToast,\n        toast.delete\n    ]);\n    function getLoadingIcon() {\n        var _toast_classNames;\n        if (icons == null ? void 0 : icons.loading) {\n            var _toast_classNames1;\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n                className: cn(classNames == null ? void 0 : classNames.loader, toast == null ? void 0 : (_toast_classNames1 = toast.classNames) == null ? void 0 : _toast_classNames1.loader, 'sonner-loader'),\n                \"data-visible\": toastType === 'loading'\n            }, icons.loading);\n        }\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Loader, {\n            className: cn(classNames == null ? void 0 : classNames.loader, toast == null ? void 0 : (_toast_classNames = toast.classNames) == null ? void 0 : _toast_classNames.loader),\n            visible: toastType === 'loading'\n        });\n    }\n    const icon = toast.icon || (icons == null ? void 0 : icons[toastType]) || getAsset(toastType);\n    var _toast_richColors, _icons_close;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"li\", {\n        tabIndex: 0,\n        ref: toastRef,\n        className: cn(className, toastClassname, classNames == null ? void 0 : classNames.toast, toast == null ? void 0 : (_toast_classNames = toast.classNames) == null ? void 0 : _toast_classNames.toast, classNames == null ? void 0 : classNames.default, classNames == null ? void 0 : classNames[toastType], toast == null ? void 0 : (_toast_classNames1 = toast.classNames) == null ? void 0 : _toast_classNames1[toastType]),\n        \"data-sonner-toast\": \"\",\n        \"data-rich-colors\": (_toast_richColors = toast.richColors) != null ? _toast_richColors : defaultRichColors,\n        \"data-styled\": !Boolean(toast.jsx || toast.unstyled || unstyled),\n        \"data-mounted\": mounted,\n        \"data-promise\": Boolean(toast.promise),\n        \"data-swiped\": isSwiped,\n        \"data-removed\": removed,\n        \"data-visible\": isVisible,\n        \"data-y-position\": y,\n        \"data-x-position\": x,\n        \"data-index\": index,\n        \"data-front\": isFront,\n        \"data-swiping\": swiping,\n        \"data-dismissible\": dismissible,\n        \"data-type\": toastType,\n        \"data-invert\": invert,\n        \"data-swipe-out\": swipeOut,\n        \"data-swipe-direction\": swipeOutDirection,\n        \"data-expanded\": Boolean(expanded || expandByDefault && mounted),\n        style: {\n            '--index': index,\n            '--toasts-before': index,\n            '--z-index': toasts.length - index,\n            '--offset': \"\".concat(removed ? offsetBeforeRemove : offset.current, \"px\"),\n            '--initial-height': expandByDefault ? 'auto' : \"\".concat(initialHeight, \"px\"),\n            ...style,\n            ...toast.style\n        },\n        onDragEnd: ()=>{\n            setSwiping(false);\n            setSwipeDirection(null);\n            pointerStartRef.current = null;\n        },\n        onPointerDown: (event)=>{\n            if (disabled || !dismissible) return;\n            dragStartTime.current = new Date();\n            setOffsetBeforeRemove(offset.current);\n            // Ensure we maintain correct pointer capture even when going outside of the toast (e.g. when swiping)\n            event.target.setPointerCapture(event.pointerId);\n            if (event.target.tagName === 'BUTTON') return;\n            setSwiping(true);\n            pointerStartRef.current = {\n                x: event.clientX,\n                y: event.clientY\n            };\n        },\n        onPointerUp: ()=>{\n            var _toastRef_current, _toastRef_current1, _dragStartTime_current;\n            if (swipeOut || !dismissible) return;\n            pointerStartRef.current = null;\n            const swipeAmountX = Number(((_toastRef_current = toastRef.current) == null ? void 0 : _toastRef_current.style.getPropertyValue('--swipe-amount-x').replace('px', '')) || 0);\n            const swipeAmountY = Number(((_toastRef_current1 = toastRef.current) == null ? void 0 : _toastRef_current1.style.getPropertyValue('--swipe-amount-y').replace('px', '')) || 0);\n            const timeTaken = new Date().getTime() - ((_dragStartTime_current = dragStartTime.current) == null ? void 0 : _dragStartTime_current.getTime());\n            const swipeAmount = swipeDirection === 'x' ? swipeAmountX : swipeAmountY;\n            const velocity = Math.abs(swipeAmount) / timeTaken;\n            if (Math.abs(swipeAmount) >= SWIPE_THRESHOLD || velocity > 0.11) {\n                setOffsetBeforeRemove(offset.current);\n                toast.onDismiss == null ? void 0 : toast.onDismiss.call(toast, toast);\n                if (swipeDirection === 'x') {\n                    setSwipeOutDirection(swipeAmountX > 0 ? 'right' : 'left');\n                } else {\n                    setSwipeOutDirection(swipeAmountY > 0 ? 'down' : 'up');\n                }\n                deleteToast();\n                setSwipeOut(true);\n                return;\n            } else {\n                var _toastRef_current2, _toastRef_current3;\n                (_toastRef_current2 = toastRef.current) == null ? void 0 : _toastRef_current2.style.setProperty('--swipe-amount-x', \"0px\");\n                (_toastRef_current3 = toastRef.current) == null ? void 0 : _toastRef_current3.style.setProperty('--swipe-amount-y', \"0px\");\n            }\n            setIsSwiped(false);\n            setSwiping(false);\n            setSwipeDirection(null);\n        },\n        onPointerMove: (event)=>{\n            var _window_getSelection, _toastRef_current, _toastRef_current1;\n            if (!pointerStartRef.current || !dismissible) return;\n            const isHighlighted = ((_window_getSelection = window.getSelection()) == null ? void 0 : _window_getSelection.toString().length) > 0;\n            if (isHighlighted) return;\n            const yDelta = event.clientY - pointerStartRef.current.y;\n            const xDelta = event.clientX - pointerStartRef.current.x;\n            var _props_swipeDirections;\n            const swipeDirections = (_props_swipeDirections = props.swipeDirections) != null ? _props_swipeDirections : getDefaultSwipeDirections(position);\n            // Determine swipe direction if not already locked\n            if (!swipeDirection && (Math.abs(xDelta) > 1 || Math.abs(yDelta) > 1)) {\n                setSwipeDirection(Math.abs(xDelta) > Math.abs(yDelta) ? 'x' : 'y');\n            }\n            let swipeAmount = {\n                x: 0,\n                y: 0\n            };\n            const getDampening = (delta)=>{\n                const factor = Math.abs(delta) / 20;\n                return 1 / (1.5 + factor);\n            };\n            // Only apply swipe in the locked direction\n            if (swipeDirection === 'y') {\n                // Handle vertical swipes\n                if (swipeDirections.includes('top') || swipeDirections.includes('bottom')) {\n                    if (swipeDirections.includes('top') && yDelta < 0 || swipeDirections.includes('bottom') && yDelta > 0) {\n                        swipeAmount.y = yDelta;\n                    } else {\n                        // Smoothly transition to dampened movement\n                        const dampenedDelta = yDelta * getDampening(yDelta);\n                        // Ensure we don't jump when transitioning to dampened movement\n                        swipeAmount.y = Math.abs(dampenedDelta) < Math.abs(yDelta) ? dampenedDelta : yDelta;\n                    }\n                }\n            } else if (swipeDirection === 'x') {\n                // Handle horizontal swipes\n                if (swipeDirections.includes('left') || swipeDirections.includes('right')) {\n                    if (swipeDirections.includes('left') && xDelta < 0 || swipeDirections.includes('right') && xDelta > 0) {\n                        swipeAmount.x = xDelta;\n                    } else {\n                        // Smoothly transition to dampened movement\n                        const dampenedDelta = xDelta * getDampening(xDelta);\n                        // Ensure we don't jump when transitioning to dampened movement\n                        swipeAmount.x = Math.abs(dampenedDelta) < Math.abs(xDelta) ? dampenedDelta : xDelta;\n                    }\n                }\n            }\n            if (Math.abs(swipeAmount.x) > 0 || Math.abs(swipeAmount.y) > 0) {\n                setIsSwiped(true);\n            }\n            (_toastRef_current = toastRef.current) == null ? void 0 : _toastRef_current.style.setProperty('--swipe-amount-x', \"\".concat(swipeAmount.x, \"px\"));\n            (_toastRef_current1 = toastRef.current) == null ? void 0 : _toastRef_current1.style.setProperty('--swipe-amount-y', \"\".concat(swipeAmount.y, \"px\"));\n        }\n    }, closeButton && !toast.jsx && toastType !== 'loading' ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"aria-label\": closeButtonAriaLabel,\n        \"data-disabled\": disabled,\n        \"data-close-button\": true,\n        onClick: disabled || !dismissible ? ()=>{} : ()=>{\n            deleteToast();\n            toast.onDismiss == null ? void 0 : toast.onDismiss.call(toast, toast);\n        },\n        className: cn(classNames == null ? void 0 : classNames.closeButton, toast == null ? void 0 : (_toast_classNames2 = toast.classNames) == null ? void 0 : _toast_classNames2.closeButton)\n    }, (_icons_close = icons == null ? void 0 : icons.close) != null ? _icons_close : CloseIcon) : null, (toastType || toast.icon || toast.promise) && toast.icon !== null && ((icons == null ? void 0 : icons[toastType]) !== null || toast.icon) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-icon\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.icon, toast == null ? void 0 : (_toast_classNames3 = toast.classNames) == null ? void 0 : _toast_classNames3.icon)\n    }, toast.promise || toast.type === 'loading' && !toast.icon ? toast.icon || getLoadingIcon() : null, toast.type !== 'loading' ? icon : null) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-content\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.content, toast == null ? void 0 : (_toast_classNames4 = toast.classNames) == null ? void 0 : _toast_classNames4.content)\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-title\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.title, toast == null ? void 0 : (_toast_classNames5 = toast.classNames) == null ? void 0 : _toast_classNames5.title)\n    }, toast.jsx ? toast.jsx : typeof toast.title === 'function' ? toast.title() : toast.title), toast.description ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-description\": \"\",\n        className: cn(descriptionClassName, toastDescriptionClassname, classNames == null ? void 0 : classNames.description, toast == null ? void 0 : (_toast_classNames6 = toast.classNames) == null ? void 0 : _toast_classNames6.description)\n    }, typeof toast.description === 'function' ? toast.description() : toast.description) : null), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(toast.cancel) ? toast.cancel : toast.cancel && isAction(toast.cancel) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": true,\n        \"data-cancel\": true,\n        style: toast.cancelButtonStyle || cancelButtonStyle,\n        onClick: (event)=>{\n            // We need to check twice because typescript\n            if (!isAction(toast.cancel)) return;\n            if (!dismissible) return;\n            toast.cancel.onClick == null ? void 0 : toast.cancel.onClick.call(toast.cancel, event);\n            deleteToast();\n        },\n        className: cn(classNames == null ? void 0 : classNames.cancelButton, toast == null ? void 0 : (_toast_classNames7 = toast.classNames) == null ? void 0 : _toast_classNames7.cancelButton)\n    }, toast.cancel.label) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(toast.action) ? toast.action : toast.action && isAction(toast.action) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": true,\n        \"data-action\": true,\n        style: toast.actionButtonStyle || actionButtonStyle,\n        onClick: (event)=>{\n            // We need to check twice because typescript\n            if (!isAction(toast.action)) return;\n            toast.action.onClick == null ? void 0 : toast.action.onClick.call(toast.action, event);\n            if (event.defaultPrevented) return;\n            deleteToast();\n        },\n        className: cn(classNames == null ? void 0 : classNames.actionButton, toast == null ? void 0 : (_toast_classNames8 = toast.classNames) == null ? void 0 : _toast_classNames8.actionButton)\n    }, toast.action.label) : null);\n};\n_s1(Toast, \"Hs2RwklMUctKsF2fEbXUzesmn3w=\", false, function() {\n    return [\n        useIsDocumentHidden\n    ];\n});\n_c1 = Toast;\nfunction getDocumentDirection() {\n    if (typeof window === 'undefined') return 'ltr';\n    if (typeof document === 'undefined') return 'ltr'; // For Fresh purpose\n    const dirAttribute = document.documentElement.getAttribute('dir');\n    if (dirAttribute === 'auto' || !dirAttribute) {\n        return window.getComputedStyle(document.documentElement).direction;\n    }\n    return dirAttribute;\n}\nfunction assignOffset(defaultOffset, mobileOffset) {\n    const styles = {};\n    [\n        defaultOffset,\n        mobileOffset\n    ].forEach((offset, index)=>{\n        const isMobile = index === 1;\n        const prefix = isMobile ? '--mobile-offset' : '--offset';\n        const defaultValue = isMobile ? MOBILE_VIEWPORT_OFFSET : VIEWPORT_OFFSET;\n        function assignAll(offset) {\n            [\n                'top',\n                'right',\n                'bottom',\n                'left'\n            ].forEach((key)=>{\n                styles[\"\".concat(prefix, \"-\").concat(key)] = typeof offset === 'number' ? \"\".concat(offset, \"px\") : offset;\n            });\n        }\n        if (typeof offset === 'number' || typeof offset === 'string') {\n            assignAll(offset);\n        } else if (typeof offset === 'object') {\n            [\n                'top',\n                'right',\n                'bottom',\n                'left'\n            ].forEach((key)=>{\n                if (offset[key] === undefined) {\n                    styles[\"\".concat(prefix, \"-\").concat(key)] = defaultValue;\n                } else {\n                    styles[\"\".concat(prefix, \"-\").concat(key)] = typeof offset[key] === 'number' ? \"\".concat(offset[key], \"px\") : offset[key];\n                }\n            });\n        } else {\n            assignAll(defaultValue);\n        }\n    });\n    return styles;\n}\nfunction useSonner() {\n    _s2();\n    const [activeToasts, setActiveToasts] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useSonner.useEffect\": ()=>{\n            return ToastState.subscribe({\n                \"useSonner.useEffect\": (toast)=>{\n                    if (toast.dismiss) {\n                        setTimeout({\n                            \"useSonner.useEffect\": ()=>{\n                                react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync({\n                                    \"useSonner.useEffect\": ()=>{\n                                        setActiveToasts({\n                                            \"useSonner.useEffect\": (toasts)=>toasts.filter({\n                                                    \"useSonner.useEffect\": (t)=>t.id !== toast.id\n                                                }[\"useSonner.useEffect\"])\n                                        }[\"useSonner.useEffect\"]);\n                                    }\n                                }[\"useSonner.useEffect\"]);\n                            }\n                        }[\"useSonner.useEffect\"]);\n                        return;\n                    }\n                    // Prevent batching, temp solution.\n                    setTimeout({\n                        \"useSonner.useEffect\": ()=>{\n                            react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync({\n                                \"useSonner.useEffect\": ()=>{\n                                    setActiveToasts({\n                                        \"useSonner.useEffect\": (toasts)=>{\n                                            const indexOfExistingToast = toasts.findIndex({\n                                                \"useSonner.useEffect.indexOfExistingToast\": (t)=>t.id === toast.id\n                                            }[\"useSonner.useEffect.indexOfExistingToast\"]);\n                                            // Update the toast if it already exists\n                                            if (indexOfExistingToast !== -1) {\n                                                return [\n                                                    ...toasts.slice(0, indexOfExistingToast),\n                                                    {\n                                                        ...toasts[indexOfExistingToast],\n                                                        ...toast\n                                                    },\n                                                    ...toasts.slice(indexOfExistingToast + 1)\n                                                ];\n                                            }\n                                            return [\n                                                toast,\n                                                ...toasts\n                                            ];\n                                        }\n                                    }[\"useSonner.useEffect\"]);\n                                }\n                            }[\"useSonner.useEffect\"]);\n                        }\n                    }[\"useSonner.useEffect\"]);\n                }\n            }[\"useSonner.useEffect\"]);\n        }\n    }[\"useSonner.useEffect\"], []);\n    return {\n        toasts: activeToasts\n    };\n}\n_s2(useSonner, \"wvKkrpl8d9UBJsfUcWYgFEOa7SA=\");\nconst Toaster = /*#__PURE__*/ _s3(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c2 = _s3(function Toaster(props, ref) {\n    _s3();\n    const { invert, position = 'bottom-right', hotkey = [\n        'altKey',\n        'KeyT'\n    ], expand, closeButton, className, offset, mobileOffset, theme = 'light', richColors, duration, style, visibleToasts = VISIBLE_TOASTS_AMOUNT, toastOptions, dir = getDocumentDirection(), gap = GAP, icons, containerAriaLabel = 'Notifications' } = props;\n    const [toasts, setToasts] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    const possiblePositions = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toaster.Toaster.useMemo[possiblePositions]\": ()=>{\n            return Array.from(new Set([\n                position\n            ].concat(toasts.filter({\n                \"Toaster.Toaster.useMemo[possiblePositions]\": (toast)=>toast.position\n            }[\"Toaster.Toaster.useMemo[possiblePositions]\"]).map({\n                \"Toaster.Toaster.useMemo[possiblePositions]\": (toast)=>toast.position\n            }[\"Toaster.Toaster.useMemo[possiblePositions]\"]))));\n        }\n    }[\"Toaster.Toaster.useMemo[possiblePositions]\"], [\n        toasts,\n        position\n    ]);\n    const [heights, setHeights] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    const [expanded, setExpanded] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [interacting, setInteracting] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [actualTheme, setActualTheme] = react__WEBPACK_IMPORTED_MODULE_0__.useState(theme !== 'system' ? theme : typeof window !== 'undefined' ? window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light' : 'light');\n    const listRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const hotkeyLabel = hotkey.join('+').replace(/Key/g, '').replace(/Digit/g, '');\n    const lastFocusedElementRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isFocusWithinRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const removeToast = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Toaster.Toaster.useCallback[removeToast]\": (toastToRemove)=>{\n            setToasts({\n                \"Toaster.Toaster.useCallback[removeToast]\": (toasts)=>{\n                    var _toasts_find;\n                    if (!((_toasts_find = toasts.find({\n                        \"Toaster.Toaster.useCallback[removeToast]\": (toast)=>toast.id === toastToRemove.id\n                    }[\"Toaster.Toaster.useCallback[removeToast]\"])) == null ? void 0 : _toasts_find.delete)) {\n                        ToastState.dismiss(toastToRemove.id);\n                    }\n                    return toasts.filter({\n                        \"Toaster.Toaster.useCallback[removeToast]\": (param)=>{\n                            let { id } = param;\n                            return id !== toastToRemove.id;\n                        }\n                    }[\"Toaster.Toaster.useCallback[removeToast]\"]);\n                }\n            }[\"Toaster.Toaster.useCallback[removeToast]\"]);\n        }\n    }[\"Toaster.Toaster.useCallback[removeToast]\"], []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            return ToastState.subscribe({\n                \"Toaster.Toaster.useEffect\": (toast)=>{\n                    if (toast.dismiss) {\n                        // Prevent batching of other state updates\n                        requestAnimationFrame({\n                            \"Toaster.Toaster.useEffect\": ()=>{\n                                setToasts({\n                                    \"Toaster.Toaster.useEffect\": (toasts)=>toasts.map({\n                                            \"Toaster.Toaster.useEffect\": (t)=>t.id === toast.id ? {\n                                                    ...t,\n                                                    delete: true\n                                                } : t\n                                        }[\"Toaster.Toaster.useEffect\"])\n                                }[\"Toaster.Toaster.useEffect\"]);\n                            }\n                        }[\"Toaster.Toaster.useEffect\"]);\n                        return;\n                    }\n                    // Prevent batching, temp solution.\n                    setTimeout({\n                        \"Toaster.Toaster.useEffect\": ()=>{\n                            react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync({\n                                \"Toaster.Toaster.useEffect\": ()=>{\n                                    setToasts({\n                                        \"Toaster.Toaster.useEffect\": (toasts)=>{\n                                            const indexOfExistingToast = toasts.findIndex({\n                                                \"Toaster.Toaster.useEffect.indexOfExistingToast\": (t)=>t.id === toast.id\n                                            }[\"Toaster.Toaster.useEffect.indexOfExistingToast\"]);\n                                            // Update the toast if it already exists\n                                            if (indexOfExistingToast !== -1) {\n                                                return [\n                                                    ...toasts.slice(0, indexOfExistingToast),\n                                                    {\n                                                        ...toasts[indexOfExistingToast],\n                                                        ...toast\n                                                    },\n                                                    ...toasts.slice(indexOfExistingToast + 1)\n                                                ];\n                                            }\n                                            return [\n                                                toast,\n                                                ...toasts\n                                            ];\n                                        }\n                                    }[\"Toaster.Toaster.useEffect\"]);\n                                }\n                            }[\"Toaster.Toaster.useEffect\"]);\n                        }\n                    }[\"Toaster.Toaster.useEffect\"]);\n                }\n            }[\"Toaster.Toaster.useEffect\"]);\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        toasts\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            if (theme !== 'system') {\n                setActualTheme(theme);\n                return;\n            }\n            if (theme === 'system') {\n                // check if current preference is dark\n                if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {\n                    // it's currently dark\n                    setActualTheme('dark');\n                } else {\n                    // it's not dark\n                    setActualTheme('light');\n                }\n            }\n            if (typeof window === 'undefined') return;\n            const darkMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n            try {\n                // Chrome & Firefox\n                darkMediaQuery.addEventListener('change', {\n                    \"Toaster.Toaster.useEffect\": (param)=>{\n                        let { matches } = param;\n                        if (matches) {\n                            setActualTheme('dark');\n                        } else {\n                            setActualTheme('light');\n                        }\n                    }\n                }[\"Toaster.Toaster.useEffect\"]);\n            } catch (error) {\n                // Safari < 14\n                darkMediaQuery.addListener({\n                    \"Toaster.Toaster.useEffect\": (param)=>{\n                        let { matches } = param;\n                        try {\n                            if (matches) {\n                                setActualTheme('dark');\n                            } else {\n                                setActualTheme('light');\n                            }\n                        } catch (e) {\n                            console.error(e);\n                        }\n                    }\n                }[\"Toaster.Toaster.useEffect\"]);\n            }\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        theme\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            // Ensure expanded is always false when no toasts are present / only one left\n            if (toasts.length <= 1) {\n                setExpanded(false);\n            }\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        toasts\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"Toaster.Toaster.useEffect.handleKeyDown\": (event)=>{\n                    var _listRef_current;\n                    const isHotkeyPressed = hotkey.every({\n                        \"Toaster.Toaster.useEffect.handleKeyDown.isHotkeyPressed\": (key)=>event[key] || event.code === key\n                    }[\"Toaster.Toaster.useEffect.handleKeyDown.isHotkeyPressed\"]);\n                    if (isHotkeyPressed) {\n                        var _listRef_current1;\n                        setExpanded(true);\n                        (_listRef_current1 = listRef.current) == null ? void 0 : _listRef_current1.focus();\n                    }\n                    if (event.code === 'Escape' && (document.activeElement === listRef.current || ((_listRef_current = listRef.current) == null ? void 0 : _listRef_current.contains(document.activeElement)))) {\n                        setExpanded(false);\n                    }\n                }\n            }[\"Toaster.Toaster.useEffect.handleKeyDown\"];\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"Toaster.Toaster.useEffect\": ()=>document.removeEventListener('keydown', handleKeyDown)\n            })[\"Toaster.Toaster.useEffect\"];\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        hotkey\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            if (listRef.current) {\n                return ({\n                    \"Toaster.Toaster.useEffect\": ()=>{\n                        if (lastFocusedElementRef.current) {\n                            lastFocusedElementRef.current.focus({\n                                preventScroll: true\n                            });\n                            lastFocusedElementRef.current = null;\n                            isFocusWithinRef.current = false;\n                        }\n                    }\n                })[\"Toaster.Toaster.useEffect\"];\n            }\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        listRef.current\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"section\", {\n        ref: ref,\n        \"aria-label\": \"\".concat(containerAriaLabel, \" \").concat(hotkeyLabel),\n        tabIndex: -1,\n        \"aria-live\": \"polite\",\n        \"aria-relevant\": \"additions text\",\n        \"aria-atomic\": \"false\",\n        suppressHydrationWarning: true\n    }, possiblePositions.map((position, index)=>{\n        var _heights_;\n        const [y, x] = position.split('-');\n        if (!toasts.length) return null;\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"ol\", {\n            key: position,\n            dir: dir === 'auto' ? getDocumentDirection() : dir,\n            tabIndex: -1,\n            ref: listRef,\n            className: className,\n            \"data-sonner-toaster\": true,\n            \"data-sonner-theme\": actualTheme,\n            \"data-y-position\": y,\n            \"data-lifted\": expanded && toasts.length > 1 && !expand,\n            \"data-x-position\": x,\n            style: {\n                '--front-toast-height': \"\".concat(((_heights_ = heights[0]) == null ? void 0 : _heights_.height) || 0, \"px\"),\n                '--width': \"\".concat(TOAST_WIDTH, \"px\"),\n                '--gap': \"\".concat(gap, \"px\"),\n                ...style,\n                ...assignOffset(offset, mobileOffset)\n            },\n            onBlur: (event)=>{\n                if (isFocusWithinRef.current && !event.currentTarget.contains(event.relatedTarget)) {\n                    isFocusWithinRef.current = false;\n                    if (lastFocusedElementRef.current) {\n                        lastFocusedElementRef.current.focus({\n                            preventScroll: true\n                        });\n                        lastFocusedElementRef.current = null;\n                    }\n                }\n            },\n            onFocus: (event)=>{\n                const isNotDismissible = event.target instanceof HTMLElement && event.target.dataset.dismissible === 'false';\n                if (isNotDismissible) return;\n                if (!isFocusWithinRef.current) {\n                    isFocusWithinRef.current = true;\n                    lastFocusedElementRef.current = event.relatedTarget;\n                }\n            },\n            onMouseEnter: ()=>setExpanded(true),\n            onMouseMove: ()=>setExpanded(true),\n            onMouseLeave: ()=>{\n                // Avoid setting expanded to false when interacting with a toast, e.g. swiping\n                if (!interacting) {\n                    setExpanded(false);\n                }\n            },\n            onDragEnd: ()=>setExpanded(false),\n            onPointerDown: (event)=>{\n                const isNotDismissible = event.target instanceof HTMLElement && event.target.dataset.dismissible === 'false';\n                if (isNotDismissible) return;\n                setInteracting(true);\n            },\n            onPointerUp: ()=>setInteracting(false)\n        }, toasts.filter((toast)=>!toast.position && index === 0 || toast.position === position).map((toast, index)=>{\n            var _toastOptions_duration, _toastOptions_closeButton;\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Toast, {\n                key: toast.id,\n                icons: icons,\n                index: index,\n                toast: toast,\n                defaultRichColors: richColors,\n                duration: (_toastOptions_duration = toastOptions == null ? void 0 : toastOptions.duration) != null ? _toastOptions_duration : duration,\n                className: toastOptions == null ? void 0 : toastOptions.className,\n                descriptionClassName: toastOptions == null ? void 0 : toastOptions.descriptionClassName,\n                invert: invert,\n                visibleToasts: visibleToasts,\n                closeButton: (_toastOptions_closeButton = toastOptions == null ? void 0 : toastOptions.closeButton) != null ? _toastOptions_closeButton : closeButton,\n                interacting: interacting,\n                position: position,\n                style: toastOptions == null ? void 0 : toastOptions.style,\n                unstyled: toastOptions == null ? void 0 : toastOptions.unstyled,\n                classNames: toastOptions == null ? void 0 : toastOptions.classNames,\n                cancelButtonStyle: toastOptions == null ? void 0 : toastOptions.cancelButtonStyle,\n                actionButtonStyle: toastOptions == null ? void 0 : toastOptions.actionButtonStyle,\n                closeButtonAriaLabel: toastOptions == null ? void 0 : toastOptions.closeButtonAriaLabel,\n                removeToast: removeToast,\n                toasts: toasts.filter((t)=>t.position == toast.position),\n                heights: heights.filter((h)=>h.position == toast.position),\n                setHeights: setHeights,\n                expandByDefault: expand,\n                gap: gap,\n                expanded: expanded,\n                swipeDirections: props.swipeDirections\n            });\n        }));\n    }));\n}, \"oqEGKFhGV9uIBJI/pmW6D0z1xPo=\")), \"oqEGKFhGV9uIBJI/pmW6D0z1xPo=\");\n_c3 = Toaster;\n\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"Loader\");\n$RefreshReg$(_c1, \"Toast\");\n$RefreshReg$(_c2, \"Toaster$React.forwardRef\");\n$RefreshReg$(_c3, \"Toaster\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/sonner/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api/college.ts":
/*!********************************!*\
  !*** ./src/lib/api/college.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCollege: () => (/* binding */ createCollege),\n/* harmony export */   deleteCollege: () => (/* binding */ deleteCollege),\n/* harmony export */   getCollegeAnalytics: () => (/* binding */ getCollegeAnalytics),\n/* harmony export */   getCollegeById: () => (/* binding */ getCollegeById),\n/* harmony export */   getColleges: () => (/* binding */ getColleges),\n/* harmony export */   getQuestionPaperStatsByDateRange: () => (/* binding */ getQuestionPaperStatsByDateRange),\n/* harmony export */   updateCollege: () => (/* binding */ updateCollege)\n/* harmony export */ });\n/* harmony import */ var _lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/utils/errorHandler */ \"(app-pages-browser)/./src/lib/utils/errorHandler.ts\");\n\n/**\n * Create a new college\n * @param collegeData The college data\n * @returns The created college\n */ async function createCollege(collegeData) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(\"Authentication required\", \"Authentication required. Please log in again.\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(\"\".concat(baseUrl, \"/colleges\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(token)\n            },\n            body: JSON.stringify(collegeData)\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(errorData.message || \"Error: \".concat(response.status), \"Failed to create college. Please try again.\");\n        }\n        const result = await response.json();\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.createSuccessResponse)(result, true, \"College created successfully!\");\n    } catch (error) {\n        console.error(\"Error creating college:\", error);\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(error instanceof Error ? error.message : \"Failed to create college. Please try again.\", \"Failed to create college. Please try again.\");\n    }\n}\n/**\n * Get all colleges\n * @returns List of colleges\n */ async function getColleges() {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(\"\".concat(baseUrl, \"/colleges\"), {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching colleges:\", error);\n        throw error;\n    }\n}\n/**\n * Delete a college\n * @param id College ID\n * @returns The deleted college\n */ async function deleteCollege(id) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(\"\".concat(baseUrl, \"/colleges/\").concat(id), {\n            method: \"DELETE\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error deleting college:\", error);\n        throw error;\n    }\n}\n/**\n * Get a college by ID\n * @param id College ID\n * @returns The college\n */ async function getCollegeById(id) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(\"\".concat(baseUrl, \"/colleges/\").concat(id), {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching college:\", error);\n        throw error;\n    }\n}\n/**\n * Update a college\n * @param id College ID\n * @param collegeData The updated college data\n * @returns The updated college\n */ async function updateCollege(id, collegeData) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(\"\".concat(baseUrl, \"/colleges/\").concat(id), {\n            method: \"PUT\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(token)\n            },\n            body: JSON.stringify(collegeData)\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error updating college:\", error);\n        throw error;\n    }\n}\n/**\n * Get a college by ID\n * @param id College ID\n * @returns The college\n */ async function getCollegeAnalytics(id) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(\"\".concat(baseUrl, \"/analytics/college/\").concat(id, \"/summary\"), {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching college:\", error);\n        throw error;\n    }\n}\n/**\n * Get daily-wise question paper statistics by subject for a college\n * @param id College ID\n * @param startDate ISO start date string\n * @param endDate ISO end date string\n * @returns Statistics data\n */ async function getQuestionPaperStatsByDateRange(id, startDate, endDate) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const url = \"\".concat(baseUrl, \"/analytics/college/\").concat(id, \"/question-papers?startDate=\").concat(encodeURIComponent(startDate), \"&endDate=\").concat(encodeURIComponent(endDate));\n        const response = await fetch(url, {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token),\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching question paper stats:\", error);\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/college.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/utils/errorHandler.ts":
/*!***************************************!*\
  !*** ./src/lib/utils/errorHandler.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSuccessResponse: () => (/* binding */ createSuccessResponse),\n/* harmony export */   handleApiError: () => (/* binding */ handleApiError),\n/* harmony export */   isApiError: () => (/* binding */ isApiError),\n/* harmony export */   isApiSuccess: () => (/* binding */ isApiSuccess),\n/* harmony export */   safeApiCall: () => (/* binding */ safeApiCall)\n/* harmony export */ });\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n\n/**\n * Handles API errors and shows appropriate user notifications\n * @param error - The error object or message\n * @param defaultMessage - Default message to show if error message is not available\n * @param showToast - Whether to show a toast notification (default: true)\n * @returns Formatted error object\n */ function handleApiError(error) {\n    let defaultMessage = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'An error occurred. Please try again.', showToast = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;\n    var _error_response_data, _error_response, _error_data, _error_response1;\n    let errorMessage = defaultMessage;\n    let statusCode;\n    // Extract error message from different error formats\n    if (error === null || error === void 0 ? void 0 : error.message) {\n        errorMessage = error.message;\n    } else if (typeof error === 'string') {\n        errorMessage = error;\n    } else if (error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n        errorMessage = error.response.data.message;\n    } else if (error === null || error === void 0 ? void 0 : (_error_data = error.data) === null || _error_data === void 0 ? void 0 : _error_data.message) {\n        errorMessage = error.data.message;\n    }\n    // Extract status code if available\n    if (error === null || error === void 0 ? void 0 : error.status) {\n        statusCode = error.status;\n    } else if (error === null || error === void 0 ? void 0 : (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) {\n        statusCode = error.response.status;\n    }\n    // Improve error messages based on common patterns\n    if (errorMessage.includes('already exists')) {\n    // Keep the original message for duplicate entries as it's already user-friendly\n    } else if (errorMessage.includes('Authentication') || errorMessage.includes('Unauthorized')) {\n        errorMessage = 'Please log in again to continue. Your session may have expired.';\n    } else if (errorMessage.includes('Network') || errorMessage.includes('fetch')) {\n        errorMessage = 'Please check your internet connection and try again.';\n    } else if (errorMessage.includes('not found')) {\n        errorMessage = 'The requested resource was not found.';\n    } else if (errorMessage.includes('Forbidden')) {\n        errorMessage = 'You do not have permission to perform this action.';\n    } else if (statusCode === 500) {\n        errorMessage = 'Server error. Please try again later.';\n    } else if (statusCode === 503) {\n        errorMessage = 'Service temporarily unavailable. Please try again later.';\n    }\n    // Show toast notification if requested\n    if (showToast) {\n        sonner__WEBPACK_IMPORTED_MODULE_0__.toast.error(errorMessage);\n    }\n    return {\n        success: false,\n        error: errorMessage,\n        statusCode\n    };\n}\n/**\n * Creates a success response object\n * @param data - The success data\n * @param showToast - Whether to show a success toast (default: false)\n * @param successMessage - Success message to show in toast\n * @returns Success response object\n */ function createSuccessResponse(data) {\n    let showToast = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false, successMessage = arguments.length > 2 ? arguments[2] : void 0;\n    if (showToast && successMessage) {\n        sonner__WEBPACK_IMPORTED_MODULE_0__.toast.success(successMessage);\n    }\n    return {\n        success: true,\n        data\n    };\n}\n/**\n * Wrapper for API calls that handles errors consistently\n * @param apiCall - The API function to call\n * @param defaultErrorMessage - Default error message\n * @param showErrorToast - Whether to show error toast\n * @returns Promise with ApiResponse\n */ async function safeApiCall(apiCall) {\n    let defaultErrorMessage = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'Operation failed. Please try again.', showErrorToast = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;\n    try {\n        const result = await apiCall();\n        return createSuccessResponse(result);\n    } catch (error) {\n        return handleApiError(error, defaultErrorMessage, showErrorToast);\n    }\n}\n/**\n * Checks if an API response is successful\n * @param response - The API response to check\n * @returns True if successful, false otherwise\n */ function isApiSuccess(response) {\n    return response.success === true;\n}\n/**\n * Checks if an API response is an error\n * @param response - The API response to check\n * @returns True if error, false otherwise\n */ function isApiError(response) {\n    return response.success === false;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/utils/errorHandler.ts\n"));

/***/ })

});