"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/college/teachers-list/page",{

/***/ "(app-pages-browser)/./src/app/college/teachers-list/page.tsx":
/*!************************************************!*\
  !*** ./src/app/college/teachers-list/page.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _components_Breadcrumb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Breadcrumb */ \"(app-pages-browser)/./src/components/Breadcrumb.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_table_teachers_table__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/table/teachers-table */ \"(app-pages-browser)/./src/components/table/teachers-table.tsx\");\n/* harmony import */ var _components_TeacherList_Tabs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/TeacherList/Tabs */ \"(app-pages-browser)/./src/components/TeacherList/Tabs.tsx\");\n/* harmony import */ var _components_TeacherList_AddTeacherForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/TeacherList/AddTeacherForm */ \"(app-pages-browser)/./src/components/TeacherList/AddTeacherForm.tsx\");\n/* harmony import */ var _components_TeacherList_EditTeacherForm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/TeacherList/EditTeacherForm */ \"(app-pages-browser)/./src/components/TeacherList/EditTeacherForm.tsx\");\n/* harmony import */ var _lib_api_teachers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/teachers */ \"(app-pages-browser)/./src/lib/api/teachers.ts\");\n/* harmony import */ var _lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils/errorHandler */ \"(app-pages-browser)/./src/lib/utils/errorHandler.ts\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_pagination__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/pagination */ \"(app-pages-browser)/./src/components/ui/pagination.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst TeachersListPage = ()=>{\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('view');\n    const [editingTeacher, setEditingTeacher] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [teacherToDelete, setTeacherToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isFilterModalOpen, setIsFilterModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        department: '',\n        status: 'all',\n        email: ''\n    });\n    const [departments, setDepartments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredTeachers, setFilteredTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [collegeId, setCollegeId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCollegeIdInput, setShowCollegeIdInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [manualCollegeId, setManualCollegeId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    const [totalTeachers, setTotalTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    // Add this function to handle manual collegeId setting\n    const handleManualCollegeIdSubmit = ()=>{\n        if (manualCollegeId.trim()) {\n            console.log(\"Setting manual collegeId:\", manualCollegeId);\n            setCollegeId(manualCollegeId);\n            localStorage.setItem('collegeId', manualCollegeId);\n            setShowCollegeIdInput(false);\n            refetch();\n        }\n    };\n    // Add this useEffect to get collegeId from multiple sources\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TeachersListPage.useEffect\": ()=>{\n            // Try to get collegeId from localStorage\n            const storedCollegeId = localStorage.getItem('collegeId');\n            if (storedCollegeId) {\n                console.log(\"Found collegeId in localStorage:\", storedCollegeId);\n                setCollegeId(storedCollegeId);\n                return;\n            }\n            // If not found in localStorage, try to extract from JWT token\n            try {\n                const possibleTokenKeys = [\n                    'token',\n                    'backendToken',\n                    'authToken',\n                    'jwtToken'\n                ];\n                for (const key of possibleTokenKeys){\n                    const token = localStorage.getItem(key);\n                    if (token) {\n                        try {\n                            const parts = token.split('.');\n                            if (parts.length === 3) {\n                                const payload = JSON.parse(atob(parts[1]));\n                                console.log(\"JWT payload:\", payload);\n                                if (payload.collegeId) {\n                                    console.log(\"Found collegeId in \".concat(key, \":\"), payload.collegeId);\n                                    setCollegeId(payload.collegeId);\n                                    // Store it in localStorage for future use\n                                    localStorage.setItem('collegeId', payload.collegeId);\n                                    return;\n                                }\n                            }\n                        } catch (e) {\n                            console.error(\"Error parsing token from \".concat(key, \":\"), e);\n                        }\n                    }\n                }\n                // Log all localStorage keys for debugging\n                console.log(\"All localStorage keys:\", Object.keys(localStorage));\n                console.error(\"Could not find collegeId in any token or localStorage\");\n            } catch (error) {\n                console.error('Error getting collegeId:', error);\n            }\n        }\n    }[\"TeachersListPage.useEffect\"], []);\n    // Fetch teachers data\n    const { data: teachersData = [], isLoading, refetch, error } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery)({\n        queryKey: [\n            'teachers',\n            collegeId,\n            currentPage,\n            pageSize,\n            filters\n        ],\n        queryFn: {\n            \"TeachersListPage.useQuery\": async ()=>{\n                if (!collegeId) {\n                    console.log(\"No collegeId found, returning empty array\");\n                    return [];\n                }\n                try {\n                    console.log(\"Fetching teachers for collegeId: \".concat(collegeId, \", page: \").concat(currentPage, \", limit: \").concat(pageSize));\n                    // Create filter object for API\n                    const apiFilters = {};\n                    if (filters.name) apiFilters.name = filters.name;\n                    if (filters.email) apiFilters.email = filters.email;\n                    if (filters.department && filters.department !== 'all_departments') {\n                        apiFilters.department = filters.department;\n                    }\n                    if (filters.status && filters.status !== 'all') {\n                        apiFilters.status = filters.status;\n                    }\n                    const response = await (0,_lib_api_teachers__WEBPACK_IMPORTED_MODULE_8__.getCollegeTeachers)(collegeId, currentPage, pageSize, apiFilters);\n                    console.log(\"API returned response:\", response);\n                    // Check if the API call was successful\n                    if (!(0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_9__.isApiSuccess)(response)) {\n                        // Error is already handled by the API function (toast shown)\n                        return [];\n                    }\n                    const data = response.data;\n                    // Handle both response formats (array or object with teachers property)\n                    let teachersArray = [];\n                    let total = 0;\n                    let totalPages = 1;\n                    if (Array.isArray(data)) {\n                        // API returned an array directly\n                        teachersArray = data;\n                        total = data.length;\n                        totalPages = 1;\n                    } else if (data && data.teachers) {\n                        // API returned an object with teachers property\n                        teachersArray = data.teachers;\n                        total = data.total || data.teachers.length;\n                        totalPages = data.totalPages || Math.ceil(total / pageSize);\n                    } else {\n                        console.error(\"API returned invalid data format:\", data);\n                        return [];\n                    }\n                    // Set total counts for pagination\n                    setTotalTeachers(total);\n                    setTotalPages(totalPages);\n                    // Transform the data to match the TeacherData interface\n                    const transformedData = teachersArray.map({\n                        \"TeachersListPage.useQuery.transformedData\": (teacher)=>{\n                            return {\n                                id: teacher._id,\n                                name: teacher.displayName || teacher.name || 'Unknown',\n                                email: teacher.email || 'No email',\n                                department: teacher.department || 'N/A',\n                                phone: teacher.phone || 'N/A',\n                                status: teacher.status === 'active' ? 'Active' : 'Inactive',\n                                avatar: teacher.avatar || null\n                            };\n                        }\n                    }[\"TeachersListPage.useQuery.transformedData\"]);\n                    return transformedData;\n                } catch (error) {\n                    console.error('Failed to fetch teachers:', error);\n                    sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('Failed to load teachers. Please try again.');\n                    return [];\n                }\n            }\n        }[\"TeachersListPage.useQuery\"],\n        enabled: !!collegeId,\n        staleTime: 0,\n        refetchOnWindowFocus: true\n    });\n    // Add debugging to see if there's an error\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TeachersListPage.useEffect\": ()=>{\n            if (error) {\n                console.error(\"Query error:\", error);\n            }\n        }\n    }[\"TeachersListPage.useEffect\"], [\n        error\n    ]);\n    // Extract unique departments for filter dropdown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TeachersListPage.useEffect\": ()=>{\n            console.log(\"teachersData changed:\", teachersData);\n            console.log(\"teachersData length:\", (teachersData === null || teachersData === void 0 ? void 0 : teachersData.length) || 0);\n            if (teachersData && teachersData.length > 0) {\n                const uniqueDepartments = Array.from(new Set(teachersData.map({\n                    \"TeachersListPage.useEffect.uniqueDepartments\": (teacher)=>teacher.department\n                }[\"TeachersListPage.useEffect.uniqueDepartments\"]).filter(Boolean)));\n                setDepartments(uniqueDepartments);\n                // Make sure we're setting the filtered teachers to the full data set initially\n                setFilteredTeachers([\n                    ...teachersData\n                ]);\n                console.log(\"Setting filtered teachers to:\", teachersData.length);\n            } else {\n                console.log(\"No teachers data to set\");\n            }\n        }\n    }[\"TeachersListPage.useEffect\"], [\n        teachersData\n    ]);\n    const handleTabChange = (tab)=>{\n        console.log('Page received tab change:', tab);\n        // If switching to edit mode from view, don't clear the editing teacher\n        if (tab !== 'edit') {\n            setEditingTeacher(null);\n        }\n        // Set the active tab with a small delay to allow for animation\n        setTimeout(()=>{\n            setActiveTab(tab);\n        }, 100);\n    };\n    const handleTeacherAdded = ()=>{\n        refetch(); // Refresh the teachers list\n        setActiveTab('view'); // Switch back to view tab\n    };\n    const handleEdit = (id)=>{\n        console.log(\"Edit teacher with id: \".concat(id));\n        const teacher = teachersData.find((t)=>t.id === id);\n        if (teacher) {\n            setEditingTeacher(teacher);\n            setActiveTab('edit');\n        }\n    };\n    const handleDelete = (id)=>{\n        console.log(\"Delete teacher with id: \".concat(id));\n        setTeacherToDelete(id);\n        setIsDeleteDialogOpen(true);\n    };\n    const confirmDelete = async ()=>{\n        if (!teacherToDelete) return;\n        try {\n            const response = await (0,_lib_api_teachers__WEBPACK_IMPORTED_MODULE_8__.deleteTeacher)(teacherToDelete);\n            if ((0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_9__.isApiSuccess)(response)) {\n                // Success toast is already shown by the API function\n                refetch(); // Refresh the list\n            }\n        // Error case is already handled by the API function (toast shown)\n        } catch (error) {\n            // Fallback error handling for unexpected errors\n            console.error(\"Unexpected error deleting teacher:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"An unexpected error occurred. Please try again.\");\n        } finally{\n            setIsDeleteDialogOpen(false);\n            setTeacherToDelete(null);\n        }\n    };\n    const handleUpdateTeacher = async (formData)=>{\n        if (!editingTeacher) return;\n        try {\n            // Only send the allowed fields and ensure status is lowercase\n            const updateData = {\n                phone: formData.phone,\n                department: formData.department,\n                designation: formData.designation,\n                status: formData.status.toLowerCase()\n            };\n            const response = await (0,_lib_api_teachers__WEBPACK_IMPORTED_MODULE_8__.updateTeacher)(editingTeacher.id, updateData);\n            if ((0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_9__.isApiSuccess)(response)) {\n                // Success toast is already shown by the API function\n                refetch(); // Refresh the list\n                setActiveTab('view'); // Switch back to view tab\n                setEditingTeacher(null);\n            }\n        // Error case is already handled by the API function (toast shown)\n        } catch (error) {\n            // Fallback error handling for unexpected errors\n            console.error('Unexpected error updating teacher:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"An unexpected error occurred. Please try again.\");\n        }\n    };\n    const handleOpenFilterModal = ()=>{\n        setIsFilterModalOpen(true);\n    };\n    const handleFilterApply = async (filterValues)=>{\n        // Update filter state used by React Query\n        setFilters(filterValues);\n        // Reset to page 1 whenever new filter is applied\n        setCurrentPage(1);\n        // Refetch teacher list using updated filters\n        if (collegeId) {\n            refetch();\n        }\n        // Optional: Inform user when default filter (i.e., all fields empty) is applied\n        const isEmptyFilter = !filterValues.name && !filterValues.email && (!filterValues.department || filterValues.department === 'all_departments') && (!filterValues.status || filterValues.status === 'all');\n        if (isEmptyFilter) {\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.info('Showing all teachers');\n        }\n    };\n    const handlePageChange = (page)=>{\n        setCurrentPage(page);\n    };\n    const handlePageSizeChange = (size)=>{\n        setPageSize(size);\n        setCurrentPage(1); // Reset to first page when changing page size\n    };\n    const paginatedTeachers = (filteredTeachers.length > 0 ? filteredTeachers : teachersData).slice((currentPage - 1) * pageSize, currentPage * pageSize);\n    const renderTeachersTable = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_table_teachers_table__WEBPACK_IMPORTED_MODULE_4__.TeachersTable, {\n                    data: paginatedTeachers,\n                    onEdit: handleEdit,\n                    onDelete: handleDelete,\n                    itemsPerPage: pageSize,\n                    isLoading: isLoading,\n                    onRefresh: ()=>{\n                        // Reset filters and show all teachers\n                        setFilters({\n                            name: '',\n                            department: '',\n                            status: 'all',\n                            email: ''\n                        });\n                        setFilteredTeachers([]);\n                        refetch();\n                    },\n                    onFilter: handleOpenFilterModal,\n                    columns: [\n                        \"name\",\n                        \"department\",\n                        \"email\",\n                        \"phone\",\n                        \"status\",\n                        \"actions\"\n                    ]\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                    lineNumber: 350,\n                    columnNumber: 9\n                }, undefined),\n                !isLoading && teachersData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_12__.Pagination, {\n                    currentPage: currentPage,\n                    totalPages: Math.ceil((filteredTeachers.length > 0 ? filteredTeachers.length : totalTeachers) / pageSize),\n                    pageSize: pageSize,\n                    totalItems: filteredTeachers.length > 0 ? filteredTeachers.length : totalTeachers,\n                    onPageChange: handlePageChange,\n                    onPageSizeChange: handlePageSizeChange,\n                    pageSizeOptions: [\n                        5,\n                        10,\n                        20,\n                        50\n                    ]\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                    lineNumber: 372,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n            lineNumber: 349,\n            columnNumber: 7\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container py-6\",\n        children: [\n            !collegeId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 p-4 border border-yellow-300 bg-yellow-50 rounded-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-yellow-800\",\n                            children: \"College ID not found. Please enter it manually or check your login status.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                            lineNumber: 393,\n                            columnNumber: 13\n                        }, undefined),\n                        !showCollegeIdInput ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                            variant: \"outline\",\n                            onClick: ()=>setShowCollegeIdInput(true),\n                            children: \"Enter College ID Manually\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                            lineNumber: 397,\n                            columnNumber: 15\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: manualCollegeId,\n                                    onChange: (e)=>setManualCollegeId(e.target.value),\n                                    placeholder: \"Enter College ID\",\n                                    className: \"px-3 py-2 border rounded-md flex-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    onClick: handleManualCollegeIdSubmit,\n                                    children: \"Submit\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                                    lineNumber: 412,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowCollegeIdInput(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                    lineNumber: 392,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                lineNumber: 391,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold tracking-tight\",\n                                children: activeTab === 'view' ? \"Teacher's List View\" : activeTab === 'add' ? \"Add Teachers\" : activeTab === 'edit' ? \"Edit Teacher\" : \"Teacher Activity Logs\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Breadcrumb__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                items: [\n                                    {\n                                        label: 'Home',\n                                        href: '/'\n                                    },\n                                    {\n                                        label: '...',\n                                        href: '#'\n                                    },\n                                    {\n                                        label: activeTab === 'view' ? 'Teacher list' : activeTab === 'add' ? 'Add teachers' : activeTab === 'edit' ? 'Edit teacher' : 'Teacher activity logs'\n                                    }\n                                ],\n                                className: \"mt-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                        lineNumber: 427,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"rounded-lg border bg-card text-card-foreground shadow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center px-6 pt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TeacherList_Tabs__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    activeTab: activeTab === 'edit' ? 'view' : activeTab,\n                                    onTabChange: handleTabChange,\n                                    collegeIdMissing: !collegeId\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 11\n                            }, undefined),\n                            activeTab === 'view' && renderTeachersTable(),\n                            activeTab === 'add' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TeacherList_AddTeacherForm__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    onCancel: ()=>handleTabChange('view'),\n                                    onSuccess: handleTeacherAdded\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                                lineNumber: 474,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === 'edit' && editingTeacher && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TeacherList_EditTeacherForm__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    teacher: editingTeacher,\n                                    onCancel: ()=>handleTabChange('view'),\n                                    onSubmit: handleUpdateTeacher\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                                lineNumber: 483,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                        lineNumber: 447,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                lineNumber: 426,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                open: isDeleteDialogOpen,\n                onOpenChange: setIsDeleteDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                    children: \"Confirm Deletion\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogDescription, {\n                                    children: \"Are you sure you want to delete this teacher? This action cannot be undone.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                                    lineNumber: 507,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                            lineNumber: 505,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-4 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsDeleteDialogOpen(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    variant: \"destructive\",\n                                    onClick: confirmDelete,\n                                    children: \"Delete\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                                    lineNumber: 515,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                            lineNumber: 511,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                    lineNumber: 504,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n                lineNumber: 503,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\",\n        lineNumber: 389,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TeachersListPage, \"G2atMfKpCTCQtusi8mdNkpyUlu4=\", false, function() {\n    return [\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQuery\n    ];\n});\n_c = TeachersListPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TeachersListPage);\nvar _c;\n$RefreshReg$(_c, \"TeachersListPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/college/teachers-list/page.tsx\n"));

/***/ })

});