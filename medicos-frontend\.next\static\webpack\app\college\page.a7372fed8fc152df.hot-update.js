"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/college/page",{

/***/ "(app-pages-browser)/./src/lib/api/college.ts":
/*!********************************!*\
  !*** ./src/lib/api/college.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCollege: () => (/* binding */ createCollege),\n/* harmony export */   deleteCollege: () => (/* binding */ deleteCollege),\n/* harmony export */   getCollegeAnalytics: () => (/* binding */ getCollegeAnalytics),\n/* harmony export */   getCollegeById: () => (/* binding */ getCollegeById),\n/* harmony export */   getColleges: () => (/* binding */ getColleges),\n/* harmony export */   getQuestionPaperStatsByDateRange: () => (/* binding */ getQuestionPaperStatsByDateRange),\n/* harmony export */   updateCollege: () => (/* binding */ updateCollege)\n/* harmony export */ });\n/* harmony import */ var _lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/utils/errorHandler */ \"(app-pages-browser)/./src/lib/utils/errorHandler.ts\");\n\n/**\n * Create a new college\n * @param collegeData The college data\n * @returns The created college\n */ async function createCollege(collegeData) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(\"Authentication required\", \"Authentication required. Please log in again.\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(\"\".concat(baseUrl, \"/colleges\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(token)\n            },\n            body: JSON.stringify(collegeData)\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(errorData.message || \"Error: \".concat(response.status), \"Failed to create college. Please try again.\");\n        }\n        const result = await response.json();\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.createSuccessResponse)(result, true, \"College created successfully!\");\n    } catch (error) {\n        console.error(\"Error creating college:\", error);\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(error instanceof Error ? error.message : \"Failed to create college. Please try again.\", \"Failed to create college. Please try again.\");\n    }\n}\n/**\n * Get all colleges\n * @returns List of colleges\n */ async function getColleges() {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(\"Authentication required\", \"Authentication required. Please log in again.\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(\"\".concat(baseUrl, \"/colleges\"), {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(errorData.message || \"Error: \".concat(response.status), \"Failed to load colleges. Please try again.\");\n        }\n        const result = await response.json();\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.createSuccessResponse)(result);\n    } catch (error) {\n        console.error(\"Error fetching colleges:\", error);\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(error instanceof Error ? error.message : \"Failed to load colleges. Please try again.\", \"Failed to load colleges. Please try again.\");\n    }\n}\n/**\n * Delete a college\n * @param id College ID\n * @returns The deleted college\n */ async function deleteCollege(id) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(\"Authentication required\", \"Authentication required. Please log in again.\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(\"\".concat(baseUrl, \"/colleges/\").concat(id), {\n            method: \"DELETE\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(errorData.message || \"Error: \".concat(response.status), \"Failed to delete college. Please try again.\");\n        }\n        const result = await response.json();\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.createSuccessResponse)(result, true, \"College deleted successfully!\");\n    } catch (error) {\n        console.error(\"Error deleting college:\", error);\n        return (0,_lib_utils_errorHandler__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(error instanceof Error ? error.message : \"Failed to delete college. Please try again.\", \"Failed to delete college. Please try again.\");\n    }\n}\n/**\n * Get a college by ID\n * @param id College ID\n * @returns The college\n */ async function getCollegeById(id) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(\"\".concat(baseUrl, \"/colleges/\").concat(id), {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching college:\", error);\n        throw error;\n    }\n}\n/**\n * Update a college\n * @param id College ID\n * @param collegeData The updated college data\n * @returns The updated college\n */ async function updateCollege(id, collegeData) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(\"\".concat(baseUrl, \"/colleges/\").concat(id), {\n            method: \"PUT\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(token)\n            },\n            body: JSON.stringify(collegeData)\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error updating college:\", error);\n        throw error;\n    }\n}\n/**\n * Get a college by ID\n * @param id College ID\n * @returns The college\n */ async function getCollegeAnalytics(id) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(\"\".concat(baseUrl, \"/analytics/college/\").concat(id, \"/summary\"), {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching college:\", error);\n        throw error;\n    }\n}\n/**\n * Get daily-wise question paper statistics by subject for a college\n * @param id College ID\n * @param startDate ISO start date string\n * @param endDate ISO end date string\n * @returns Statistics data\n */ async function getQuestionPaperStatsByDateRange(id, startDate, endDate) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const url = \"\".concat(baseUrl, \"/analytics/college/\").concat(id, \"/question-papers?startDate=\").concat(encodeURIComponent(startDate), \"&endDate=\").concat(encodeURIComponent(endDate));\n        const response = await fetch(url, {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token),\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching question paper stats:\", error);\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/college.ts\n"));

/***/ })

});