/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-katex";
exports.ids = ["vendor-chunks/react-katex"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-katex/dist/react-katex.js":
/*!******************************************************!*\
  !*** ./node_modules/react-katex/dist/react-katex.js ***!
  \******************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval("var __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;(function(global, factory) {\n    if ( true && typeof module.exports === \"object\") factory(exports, __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\"), __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\"), __webpack_require__(/*! katex */ \"(ssr)/./node_modules/katex/dist/katex.js\"));\n    else if (true) !(__WEBPACK_AMD_DEFINE_ARRAY__ = [\n        exports,\n        __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\"),\n        __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\"),\n        __webpack_require__(/*! katex */ \"(ssr)/./node_modules/katex/dist/katex.js\")\n    ], __WEBPACK_AMD_DEFINE_FACTORY__ = (factory),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?\n\t\t(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n    else {}\n})(this, function(exports, _react, _propTypes, _katex) {\n    \"use strict\";\n    Object.defineProperty(exports, \"__esModule\", {\n        value: true\n    });\n    function _export(target, all) {\n        for(var name in all)Object.defineProperty(target, name, {\n            enumerable: true,\n            get: all[name]\n        });\n    }\n    _export(exports, {\n        BlockMath: ()=>BlockMath,\n        InlineMath: ()=>InlineMath\n    });\n    _react = /*#__PURE__*/ _interopRequireWildcard(_react);\n    _propTypes = /*#__PURE__*/ _interopRequireDefault(_propTypes);\n    _katex = /*#__PURE__*/ _interopRequireDefault(_katex);\n    function _interopRequireDefault(obj) {\n        return obj && obj.__esModule ? obj : {\n            default: obj\n        };\n    }\n    function _getRequireWildcardCache(nodeInterop) {\n        if (typeof WeakMap !== \"function\") return null;\n        var cacheBabelInterop = new WeakMap();\n        var cacheNodeInterop = new WeakMap();\n        return (_getRequireWildcardCache = function(nodeInterop) {\n            return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n        })(nodeInterop);\n    }\n    function _interopRequireWildcard(obj, nodeInterop) {\n        if (!nodeInterop && obj && obj.__esModule) {\n            return obj;\n        }\n        if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n            return {\n                default: obj\n            };\n        }\n        var cache = _getRequireWildcardCache(nodeInterop);\n        if (cache && cache.has(obj)) {\n            return cache.get(obj);\n        }\n        var newObj = {};\n        var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n        for(var key in obj){\n            if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n                var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n                if (desc && (desc.get || desc.set)) {\n                    Object.defineProperty(newObj, key, desc);\n                } else {\n                    newObj[key] = obj[key];\n                }\n            }\n        }\n        newObj.default = obj;\n        if (cache) {\n            cache.set(obj, newObj);\n        }\n        return newObj;\n    }\n    /**\n * @typedef {import(\"react\").ReactNode} ReactNode\n *\n *\n * @callback ErrorRenderer\n * @param {Error} error\n * @returns {ReactNode}\n *\n *\n * @typedef {object} MathComponentPropsWithMath\n * @property {string} math\n * @property {ReactNode=} children\n * @property {string=} errorColor\n * @property {ErrorRenderer=} renderError\n *\n *\n * @typedef {object} MathComponentPropsWithChildren\n * @property {string=} math\n * @property {ReactNode} children\n * @property {string=} errorColor\n * @property {ErrorRenderer=} renderError\n *\n * @typedef {MathComponentPropsWithMath | MathComponentPropsWithChildren} MathComponentProps\n */ const createMathComponent = (Component, { displayMode  })=>{\n        /**\n   *\n   * @param {MathComponentProps} props\n   * @returns {ReactNode}\n   */ const MathComponent = ({ children , errorColor , math , renderError  })=>{\n            const formula = math !== null && math !== void 0 ? math : children;\n            const { html , error  } = (0, _react.useMemo)(()=>{\n                try {\n                    const html = _katex.default.renderToString(formula, {\n                        displayMode,\n                        errorColor,\n                        throwOnError: !!renderError\n                    });\n                    return {\n                        html,\n                        error: undefined\n                    };\n                } catch (error) {\n                    if (error instanceof _katex.default.ParseError || error instanceof TypeError) {\n                        return {\n                            error\n                        };\n                    }\n                    throw error;\n                }\n            }, [\n                formula,\n                errorColor,\n                renderError\n            ]);\n            if (error) {\n                return renderError ? renderError(error) : /*#__PURE__*/ _react.default.createElement(Component, {\n                    html: `${error.message}`\n                });\n            }\n            return /*#__PURE__*/ _react.default.createElement(Component, {\n                html: html\n            });\n        };\n        MathComponent.propTypes = {\n            children: _propTypes.default.string,\n            errorColor: _propTypes.default.string,\n            math: _propTypes.default.string,\n            renderError: _propTypes.default.func\n        };\n        return MathComponent;\n    };\n    const InternalPathComponentPropTypes = {\n        html: _propTypes.default.string.isRequired\n    };\n    const InternalBlockMath = ({ html  })=>{\n        return /*#__PURE__*/ _react.default.createElement(\"div\", {\n            \"data-testid\": \"react-katex\",\n            dangerouslySetInnerHTML: {\n                __html: html\n            }\n        });\n    };\n    InternalBlockMath.propTypes = InternalPathComponentPropTypes;\n    const InternalInlineMath = ({ html  })=>{\n        return /*#__PURE__*/ _react.default.createElement(\"span\", {\n            \"data-testid\": \"react-katex\",\n            dangerouslySetInnerHTML: {\n                __html: html\n            }\n        });\n    };\n    InternalInlineMath.propTypes = InternalPathComponentPropTypes;\n    const BlockMath = createMathComponent(InternalBlockMath, {\n        displayMode: true\n    });\n    const InlineMath = createMathComponent(InternalInlineMath, {\n        displayMode: false\n    });\n});\n\n//# sourceMappingURL=data:application/json;base64,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\n\n//# sourceMappingURL=react-katex.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-katex/dist/react-katex.js\n");

/***/ })

};
;